import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/moderation/dto/reported_photo_data.dart';
import 'package:portraitmode/moderation/http_responses/reported_photo_list_response.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/moderation/widgets/review_photo_list_item.dart';

class ReviewReportedPhotosScreen extends ConsumerStatefulWidget {
  const ReviewReportedPhotosScreen({super.key});

  @override
  ReviewReportedPhotosScreenState createState() =>
      ReviewReportedPhotosScreenState();
}

class ReviewReportedPhotosScreenState
    extends ConsumerState<ReviewReportedPhotosScreen> {
  final _scrollController = ScrollController();
  final ModerationService _moderationService = ModerationService();

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<List<ReportedPhotoData>> _photoListNotifier =
      ValueNotifier([]);
  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _photoListNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        titleText: "Reported photos",
        useLogo: false,
        automaticallyImplyLeading: true,
      ),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(
            maxWidth: ScreenStyleConfig.maxWidth,
          ),
          child: RefreshIndicator(
            edgeOffset: LayoutConfig.bottomNavBarHeight,
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _photoListNotifier,
              builder: (context, photoList, child) {
                return ValueListenableBuilder(
                  valueListenable: _blockScrollNotifier,
                  builder: (context, blockScrolling, child) {
                    return ListView.builder(
                      controller: _scrollController,
                      cacheExtent: getVerticalScrollCacheExtent(context),
                      physics: blockScrolling
                          ? const NeverScrollableScrollPhysics()
                          : null,
                      itemCount:
                          photoList.length + (_loadMoreEndReached ? 0 : 1),
                      itemBuilder: (BuildContext context, int index) {
                        if (index == photoList.length) {
                          return Padding(
                            padding: EdgeInsets.only(
                              top: photoList.isEmpty ? 0 : 16.0,
                            ),
                            child: LinearProgressIndicator(
                              color: context.colors.baseColorAlt,
                            ),
                          );
                        }

                        final int myProfileId = LocalUserService.userId ?? 0;

                        return ReviewPhotoListItem(
                          index: index,
                          margin: EdgeInsets.only(
                            top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                          ),
                          photo: photoList[index].toPhotoData(),
                          isOwnPhoto: photoList[index].authorId == myProfileId,
                          moderationType: 'photo_report',
                          reporters: photoList[index].reporters,
                          onModerationDismissed: _handleModerationDismissed,
                          onPhotoDeleted: _handlePhotoDeleted,
                          onTwoFingersOn: () {
                            _blockScrollNotifier.value = true;
                          },
                          onTwoFingersOff: () {
                            _blockScrollNotifier.value = false;
                          },
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;
    _loadMoreLastId = 0;
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingNotifier.value = true;

    final ReportedPhotoListResponse response = await _moderationService
        .fetchReportedPhotos(limit: _loadMorePerPage, lastId: _loadMoreLastId);

    if (!mounted) return;

    _handlePhotoListResponse(response, isRefresh);
    _isFetchingNotifier.value = false;
  }

  void _handlePhotoListResponse(
    ReportedPhotoListResponse response,
    bool isRefresh,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        _photoListNotifier.value = [];
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before storing it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      _photoListNotifier.value = response.data;
    } else {
      _photoListNotifier.value = [
        ..._photoListNotifier.value,
        ...response.data,
      ];
    }
  }

  void _handleModerationDismissed(int photoId) {
    final int index = _photoListNotifier.value.indexWhere(
      (photo) => photo.id == photoId,
    );

    if (index == -1) return;

    _photoListNotifier.value = List.from(_photoListNotifier.value)
      ..removeAt(index);
  }

  void _handlePhotoDeleted(int photoId) {
    final int index = _photoListNotifier.value.indexWhere(
      (photo) => photo.id == photoId,
    );
    if (index == -1) return;

    _photoListNotifier.value = List.from(_photoListNotifier.value)
      ..removeAt(index);
  }
}
