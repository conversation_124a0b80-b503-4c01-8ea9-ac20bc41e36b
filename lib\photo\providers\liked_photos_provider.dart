// liked_photos_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class LikedPhotoIdsNotifier extends IdListNotifier {}

final likedPhotoIdsProvider =
    NotifierProvider.autoDispose<LikedPhotoIdsNotifier, List<int>>(
      LikedPhotoIdsNotifier.new,
    );

final likedPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(likedPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing liked photos reactivity.
///
/// This is the recommended way to manage latest photos.
final class LikedPhotosReactiveService {
  const LikedPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get local photo list
  List<PhotoData> getAll() {
    return ref.read(likedPhotosProvider);
  }

  /// Get local id list
  List<int> getAllIds() {
    return ref.read(likedPhotoIdsProvider);
  }

  /// Add a new photo (adds to both global store and local ids)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to local id list
    ref.read(likedPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(likedPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a local photo
  void remove(int photoId) {
    ref.read(likedPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple local photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from local id list
    ref.read(likedPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in local id list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all local photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(likedPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  // Clear the local id list
  void clear() {
    ref.read(likedPhotoIdsProvider.notifier).clear();
  }

  /// Get local id list count
  int get count => ref.read(likedPhotoIdsProvider.notifier).length;

  /// Check if local id list is empty
  bool get isEmpty => ref.read(likedPhotoIdsProvider.notifier).isEmpty;

  /// Check if local id list is not empty
  bool get isNotEmpty => ref.read(likedPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the LikedPhotosReactiveService.
final likedPhotosReactiveServiceProvider =
    Provider.autoDispose<LikedPhotosReactiveService>((ref) {
      return LikedPhotosReactiveService(ref);
    });
