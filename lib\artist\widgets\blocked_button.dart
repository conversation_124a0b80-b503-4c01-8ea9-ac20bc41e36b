import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/providers/blocked_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/artist/services/follow_artist_service.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/misc/loading_indicator.dart';

class BlockedButton extends ConsumerStatefulWidget {
  final ArtistData artist;

  const BlockedButton({super.key, required this.artist});

  @override
  BlockedButtonState createState() => BlockedButtonState();
}

class BlockedButtonState extends ConsumerState<BlockedButton> {
  final followArtistService = FollowArtistService();
  final _doingUnblockNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _doingUnblockNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(height: 35.0, child: blockedButton());
  }

  Widget blockedButton() {
    return ElevatedButton(
      onPressed: _showUnblockDialog,
      style: ElevatedButton.styleFrom(
        elevation: 0.0,
        backgroundColor: context.colors.dangerColor,
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(35.0),
        ),
      ),
      child: ValueListenableBuilder(
        valueListenable: _doingUnblockNotifier,
        builder: (context, doingUnblock, _) {
          if (doingUnblock) {
            return const SizedBox(width: 46.0, child: LoadingIndicator());
          }

          return const Text(
            'Blocked',
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 12.0,
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }

  void _showUnblockDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('Unblock @${widget.artist.nicename}?'),
        content: const Text("Are you sure you want to un-block this artist?"),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (mounted) {
                Navigator.pop(context);
              }

              _unblockArtist();
            },
            child: Text(
              'Unblock',
              style: TextStyle(color: context.colors.dangerColor),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _unblockArtist() async {
    _doingUnblockNotifier.value = true;

    final artistService = ArtistService();

    final BaseResponse response = await artistService.blockUnblockArtist(
      artistId: widget.artist.id,
      action: BlockActionType.unblock,
    );

    if (!mounted) return;

    _doingUnblockNotifier.value = false;

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      if (mounted) {
        Navigator.pop(context);
      }

      return;
    }

    ref
        .read(artistStoreProvider.notifier)
        .setIsBlocked(widget.artist.id, false);

    ref.read(blockedArtistIdsProvider.notifier).removeItem(widget.artist.id);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );
    }
  }
}
