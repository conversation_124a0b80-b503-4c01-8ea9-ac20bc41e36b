// latest_photos_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class LatestPhotoIdsNotifier extends IdListNotifier {}

final latestPhotoIdsProvider =
    NotifierProvider.autoDispose<LatestPhotoIdsNotifier, List<int>>(
      LatestPhotoIdsNotifier.new,
    );

final latestPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(latestPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing latest photos reactivity.
///
/// This is the recommended way to manage latest photos.
final class LatestPhotosReactiveService {
  const LatestPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get local photo list
  List<PhotoData> getAll() {
    return ref.read(latestPhotosProvider);
  }

  /// Get local id list
  List<int> getAllIds() {
    return ref.read(latestPhotoIdsProvider);
  }

  /// Add a new photo (adds to both global store and local ids)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to local id list
    ref.read(latestPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(latestPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a local photo
  void remove(int photoId) {
    ref.read(latestPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple local photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from local id list
    ref.read(latestPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in local id list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all local photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(latestPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  // Clear the local id list
  void clear() {
    // Clear the local id list
    ref.read(latestPhotoIdsProvider.notifier).clear();
  }

  /// Get local id list count
  int get count => ref.read(latestPhotoIdsProvider.notifier).length;

  /// Check if local id list is empty
  bool get isEmpty => ref.read(latestPhotoIdsProvider.notifier).isEmpty;

  /// Check if local id list is not empty
  bool get isNotEmpty => ref.read(latestPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the LatestPhotosReactiveService.
final latestPhotosReactiveServiceProvider =
    Provider.autoDispose<LatestPhotosReactiveService>((ref) {
      return LatestPhotosReactiveService(ref);
    });
