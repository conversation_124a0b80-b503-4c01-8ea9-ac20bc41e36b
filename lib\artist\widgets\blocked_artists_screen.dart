import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/blocked_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/artist/widgets/unblock_button.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class BlockedArtistsScreen extends ConsumerStatefulWidget {
  const BlockedArtistsScreen({super.key});

  @override
  BlockedArtistsScreenState createState() => BlockedArtistsScreenState();
}

class BlockedArtistsScreenState extends ConsumerState<BlockedArtistsScreen> {
  final _scrollController = ScrollController();
  final _artistListService = ArtistListService();

  final int _loadMorePerPage = LoadMoreConfig.smallItemsPerPage;
  int _loadMoreLastId = -1;
  int _lastTotalPhotos = -1;
  bool _loadMoreEndReached = false;

  final _isFetchingNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    pmLog('🖥️ Building BlockedArtistsScreen');

    final artistList = ref.watch(blockedArtistsProvider);

    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        titleText: "Blocked artists",
        useLogo: false,
        automaticallyImplyLeading: true,
        actions: const [],
      ),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _isFetchingNotifier,
              builder: (context, isFetching, child) {
                if (!isFetching && _loadMoreEndReached && artistList.isEmpty) {
                  return Center(
                    child: Text(
                      'No artists blocked.',
                      style: TextStyle(
                        color: context.colors.brandColor,
                        fontSize: 16.0,
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  cacheExtent: getVerticalScrollCacheExtent(context),
                  itemCount:
                      artistList.length +
                      (isFetching && !_loadMoreEndReached ? 1 : 0),
                  itemBuilder: (BuildContext context, int index) {
                    if (index == artistList.length &&
                        isFetching &&
                        !_loadMoreEndReached) {
                      return Padding(
                        padding: EdgeInsets.only(
                          top: artistList.isEmpty ? 0 : 16.0,
                        ),
                        child: LinearProgressIndicator(
                          color: context.colors.baseColorAlt,
                        ),
                      );
                    }

                    double marginTop = index == 0
                        ? LayoutConfig.contentTopGap
                        : 12.0;

                    return Padding(
                      padding: EdgeInsets.only(
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                        top: marginTop,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: ArtistListItem(
                              index: index,
                              artist: artistList[index],
                            ),
                          ),
                          const SizedBox(width: 10.0),
                          UnblockButton(artist: artistList[index]),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = -1;
    _lastTotalPhotos = -1;
    _loadMoreEndReached = false;

    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingNotifier.value = true;

    final ArtistListResponse response = await _artistListService.fetchBlocked(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      lastTotalPhotos: _lastTotalPhotos,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleArtistListResponse(response, isRefresh);
    _isFetchingNotifier.value = false;
  }

  void _handleArtistListResponse(ArtistListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final blockedArtistsReactiveService = ref.read(
      blockedArtistsReactiveServiceProvider,
    );

    final bool isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        blockedArtistsReactiveService.clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;
    _lastTotalPhotos = response.data.last.totalPhotos;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      blockedArtistsReactiveService.replaceAll(response.data);
    } else {
      blockedArtistsReactiveService.addItems(response.data);
    }
  }
}
