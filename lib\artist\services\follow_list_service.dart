// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
// Internal packages.
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/base/base_service.dart';

class FollowListService extends BaseService {
  Future<ArtistListResponse> fetchFollowings({
    int limit = 15,
    int? lastId,
    int? lastTotalPhotos,
  }) async {
    lastId ??= -1;
    lastTotalPhotos ??= -1;

    try {
      final response = await http.get(
        '${URL.baseApiUrl}/followings/${limit.toString()}/${lastId.toString()}/${lastTotalPhotos.toString()}',
      );

      return ArtistListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistListResponse.fromMap(e.response?.data);
      }

      return ArtistListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ArtistListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<ArtistListResponse> fetchFollowers({
    int limit = 15,
    int? lastId,
    int? lastTotalPhotos,
  }) async {
    lastId ??= -1;
    lastTotalPhotos ??= -1;

    try {
      final response = await http.get(
        '${URL.baseApiUrl}/followers/${limit.toString()}/${lastId.toString()}/${lastTotalPhotos.toString()}',
      );

      return ArtistListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistListResponse.fromMap(e.response?.data);
      }

      return ArtistListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ArtistListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}
