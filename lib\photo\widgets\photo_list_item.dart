// photo_list_item.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/comment_icon_button.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/like_icon_button.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/likes_count.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_description.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_frame.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_header.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/share_photo_button.dart';
import 'package:timeago/timeago.dart' as timeago;

class PhotoListItem extends ConsumerWidget {
  final PhotoData photo;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? containerWidth;
  final Alignment? alignment;
  final int? myProfileId;
  final bool? isOwnProfile;
  final String screenName;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;

  const PhotoListItem({
    super.key,
    required this.photo,
    this.margin,
    this.padding,
    this.containerWidth,
    this.alignment,
    this.myProfileId,
    this.isOwnProfile,
    required this.screenName,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final watchedPhoto = ref.watch(photoProvider(photo.id));

    // pmLog('📷 Building photo "${photo.id}" by ${photo.authorDisplayName}');

    final bool ownProfile = isOwnProfile != null
        ? isOwnProfile!
        : myProfileId != null
        ? (watchedPhoto ?? photo).authorId == myProfileId
        : false;

    DateTime uploadDate = DateTime.parse(photo.date);
    uploadDate = uploadDate.add(uploadDate.timeZoneOffset).toUtc();

    return Container(
      margin: margin,
      padding: padding,
      alignment: alignment,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              right: ScreenStyleConfig.horizontalPadding,
              left: ScreenStyleConfig.horizontalPadding,
              bottom: 6.0,
            ),
            child: PhotoHeader(
              photo: watchedPhoto ?? photo,
              screenName: screenName,
              isOwnProfile: ownProfile,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10.0),
            child: PhotoFrame(
              photo: watchedPhoto ?? photo,
              isOwnPhoto: ownProfile,
              screenName: screenName,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PhotoDetailScreen(
                      photo: watchedPhoto ?? photo,
                      originScreenName: screenName,
                    ),
                  ),
                );
              },
              onTwoFingersOn: onTwoFingersOn,
              onTwoFingersOff: onTwoFingersOff,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              right: ScreenStyleConfig.horizontalPadding,
              left: ScreenStyleConfig.horizontalPadding,
              bottom: 8.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: LikeIconButton(
                        photoId: (watchedPhoto ?? photo).id,
                        isLiked: (watchedPhoto ?? photo).isLiked,
                        totalLikes: (watchedPhoto ?? photo).totalLikes,
                        shrinkTapTarget: true,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: CommentIconButton(
                        photo: watchedPhoto ?? photo,
                        screenName: screenName,
                        shrinkTapTarget: true,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: SharePhotoButton(
                        photo: watchedPhoto ?? photo,
                        shrinkTapTarget: true,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5.0),
                LikesCount(
                  photoId: (watchedPhoto ?? photo).id,
                  totalLikes: (watchedPhoto ?? photo).totalLikes,
                ),
                (photo.description.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: PhotoDescription(
                          photo: photo,
                          screenName: screenName,
                        ),
                      )
                    : const SizedBox.shrink()),
                Text(
                  timeago.format(uploadDate),
                  style: TextStyle(
                    fontSize: 12.0,
                    color: context.colors.timeColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
