// category_detail_screen_masonry_mode.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/providers/category_photos_interaction_provider.dart';
import 'package:portraitmode/category/providers/category_photos_provider.dart';
import 'package:portraitmode/category/widgets/category_detail_screen_list_mode.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';

class CategoryDetailScreenMasonryMode extends ConsumerStatefulWidget {
  const CategoryDetailScreenMasonryMode({super.key, required this.category});

  final CategoryData category;

  @override
  CategoryDetailScreenMasonryModeState createState() =>
      CategoryDetailScreenMasonryModeState();
}

class CategoryDetailScreenMasonryModeState
    extends ConsumerState<CategoryDetailScreenMasonryMode> {
  final _scrollController = ScrollController();
  final PhotoListService photoListService = PhotoListService();

  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.gridItemsPerPage;
  bool _loadMoreEndReached = false;
  bool _fetchingData = false;

  final ValueNotifier<bool> _showLoadingIndicatorNotifier = ValueNotifier(true);
  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _showLoadingIndicatorNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_fetchingData && !_loadMoreEndReached;
  }

  void _onScroll() {
    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }

    if (shouldShowLoadingIndicator(_scrollController.position) &&
        _fetchingData) {
      if (!_showLoadingIndicatorNotifier.value) {
        _showLoadingIndicatorNotifier.value = true;
      }
    } else {
      if (_showLoadingIndicatorNotifier.value) {
        _showLoadingIndicatorNotifier.value = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final photoList = ref.watch(categoryPhotosProvider(widget.category.id));
    const double appBarHeight = LayoutConfig.bottomNavBarHeight - 10;

    /// Watch categoryPhotosInteractionProvider,
    /// otherwise it will be disposed after async requests completed.
    ref.watch(categoryPhotosInteractionProvider);

    return Scaffold(
      appBar: PmAppBar(
        height: appBarHeight,
        titleText: widget.category.name,
        useLogo: false,
        automaticallyImplyLeading: true,
        scrollController: _scrollController,
      ),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(
            maxWidth: ScreenStyleConfig.maxWidth,
          ),
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: Column(
              children: [
                Expanded(
                  child: ValueListenableBuilder(
                    valueListenable: _blockScrollNotifier,
                    builder: (context, blockScrolling, child) {
                      return MasonryGridView.count(
                        controller: _scrollController,
                        cacheExtent: getVerticalScrollCacheExtent(context),
                        padding: EdgeInsets.all(8.0),
                        crossAxisCount: 2,
                        mainAxisSpacing: 8.0,
                        crossAxisSpacing: 8.0,
                        itemCount: photoList.length,
                        itemBuilder: (BuildContext context, int index) {
                          if (index >= photoList.length) {
                            return const SizedBox.shrink();
                          }

                          return PhotoMasonryItem(
                            key: ValueKey(photoList[index].id),
                            index: index,
                            photo: photoList[index],
                            isOwnProfile:
                                photoList[index].authorId == _profileId,
                            screenName: 'camera_detail_screen',
                            onPhotoTap: () => _handlePhotoTap(index),
                          );
                        },
                      );
                    },
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: _showLoadingIndicatorNotifier,
                  builder: (context, showLoading, child) {
                    if (showLoading) {
                      return Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          SizedBox(
                            height: 2.7,
                            child: LinearProgressIndicator(
                              color: context.colors.baseColorAlt,
                            ),
                          ),
                        ],
                      );
                    }

                    return SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handlePhotoTap(int itemIndex) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryDetailScreenListMode(
          category: widget.category,
          initialScrollIndex: itemIndex,
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref
        .read(categoryPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.category.slug, 0);

    _loadMoreEndReached = false;

    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _fetchingData = true;

    final int loadMoreLastId = ref
        .read(categoryPhotosInteractionProvider.notifier)
        .getLoadMoreLastId(widget.category.slug);

    pmLog('loadMoreLastId: $loadMoreLastId');

    final isFirstLoad = loadMoreLastId == 0;

    PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      categoryId: widget.category.id,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, isRefresh, isFirstLoad);

    _fetchingData = false;

    if (_showLoadingIndicatorNotifier.value) {
      _showLoadingIndicatorNotifier.value = false;
    }
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final categoryPhotosReactiveService = ref.read(
      categoryPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        categoryPhotosReactiveService.clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    ref
        .read(categoryPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.category.slug, response.data.last.id);

    pmLog('response.data.last.id: ${response.data.last.id}');

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      categoryPhotosReactiveService.replaceAll(
        widget.category.id,
        response.data,
      );
    } else {
      categoryPhotosReactiveService.addItems(widget.category.id, response.data);
    }
  }
}
