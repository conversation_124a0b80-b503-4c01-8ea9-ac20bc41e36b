import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';

class EmptyFollowerNotice extends StatelessWidget {
  const EmptyFollowerNotice({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(right: 40.0, left: 40.0, bottom: 20.0),
      alignment: Alignment.center,
      // Add border radius and 1.0 border.
      color: context.colors.scaffoldColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Ionicons.person_outline,
            size: 56.0,
            color: context.colors.brandColor,
          ),
          const SizedBox(height: 20.0),
          Text(
            'No followers found',
            style: TextStyle(
              fontSize: 32.0,
              fontWeight: FontWeight.w600,
              color: context.colors.brandColor,
            ),
          ),
          const SizedBox(height: 16.0),

          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                TextSpan(
                  text: "You don't have any followers yet.",
                  style: TextStyle(
                    height: 1.5,
                    fontSize: 16.0,
                    fontWeight: FontWeight.normal,
                    color: context.colors.brandColorAlt,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
