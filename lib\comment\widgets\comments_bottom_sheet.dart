import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/widgets/pm_divider.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/modals/modal_drag_handle_delegate.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class CommentsBottomSheet extends ConsumerStatefulWidget {
  final PhotoData photo;

  const CommentsBottomSheet({super.key, required this.photo});

  @override
  CommentsBottomSheetState createState() => CommentsBottomSheetState();
}

class CommentsBottomSheetState extends ConsumerState<CommentsBottomSheet> {
  final _commentListService = CommentListService();
  final _commentFieldController = TextEditingController();
  final _focusNode = FocusNode();
  final _bottomSheetController = DraggableScrollableController();
  final _photoDescriptionKey = GlobalKey();

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _pageIndex = 0;
  bool _loadMoreEndReached = false;

  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<Map<int, GlobalKey>> _activeCommentKeysNotifier =
      ValueNotifier({});

  int? _activeCommentId;
  late int _profileId;

  @override
  void initState() {
    super.initState();

    _focusNode.addListener(_handleFocusChange);
    _profileId = LocalUserService.userId ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore();
    });
  }

  @override
  void dispose() {
    _activeCommentKeysNotifier.value.clear();

    _commentFieldController.dispose();
    _focusNode.removeListener(_handleFocusChange);
    _bottomSheetController.dispose();
    _focusNode.dispose();

    _isFetchingNotifier.dispose();
    _activeCommentKeysNotifier.dispose();

    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus) {
      pmLog('Field is focused, scroll the DraggableScrollableSheet up');
      // Field is focused, scroll the DraggableScrollableSheet up
      _bottomSheetController.animateTo(
        0.9,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll(ScrollMetrics metrics) {
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(metrics.maxScrollExtent);

    // Handle load more when scrolling reaches the trigger point
    if (metrics.pixels >= triggerPoint) {
      _loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      controller: _bottomSheetController,
      maxChildSize: BottomSheetConfig.maxChildSize,
      initialChildSize: 0.6,
      expand: false,
      builder: (BuildContext context, ScrollController scrollController) {
        return SafeArea(
          child: Column(
            children: [
              Expanded(child: _buildCommentList(scrollController)),
              Column(
                children: [
                  ValueListenableBuilder(
                    valueListenable: _isFetchingNotifier,
                    builder: (context, isFetching, _) {
                      if (!isFetching) return const SizedBox.shrink();

                      return SizedBox(
                        height: 2.7,
                        child: LinearProgressIndicator(
                          color: context.colors.baseColorAlt,
                        ),
                      );
                    },
                  ),
                  ValueListenableBuilder(
                    valueListenable: _activeCommentKeysNotifier,
                    builder: (context, activeCommentKeys, _) {
                      return CommentForm(
                        photoId: widget.photo.id,
                        fieldController: _commentFieldController,
                        focusNode: _focusNode,
                        photoDescriptionKey: _photoDescriptionKey,
                        activeCommentKey:
                            _activeCommentKeysNotifier.value[_activeCommentId],
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCommentList(ScrollController scrollController) {
    final List<CommentData> commentList = ref.watch(
      commentListProvider(widget.photo.id),
    );

    if (!_isFetchingNotifier.value &&
        _loadMoreEndReached &&
        commentList.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        alignment: Alignment.center,
        child: Text(
          'No comments found',
          style: TextStyle(color: context.colors.primarySwatch[300]),
        ),
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        _onScroll(scrollInfo.metrics);

        return false; // Allow the notification to continue
      },
      child: CustomScrollView(
        controller: scrollController,
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: ModalDragHandleDelegate(),
          ),

          SliverToBoxAdapter(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 3.0, bottom: 16.0),
                  child: Text(
                    "Comments",
                    style: TextStyle(
                      fontSize: 15.0,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                PmDivider(height: 0.7),
              ],
            ),
          ),

          // List of comments
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final bool isOwnComment =
                  commentList[index].authorId == _profileId;

              return CommentListItem(
                key: ValueKey('comment_${commentList[index].id}'),
                commentFieldController: _commentFieldController,
                commentFieldFocusNode: _focusNode,
                comment: commentList[index],
                isOwnComment: isOwnComment,
                margin: EdgeInsets.only(top: 12.0),
                onEditCommentTap: isOwnComment
                    ? () {
                        _activeCommentId = commentList[index].id;

                        final Map<int, GlobalKey> activeCommentKeys = Map.from(
                          _activeCommentKeysNotifier.value,
                        );

                        activeCommentKeys[commentList[index].id] = GlobalKey();
                        _activeCommentKeysNotifier.value = activeCommentKeys;

                        handleEditCommentTap(
                          ref: ref,
                          comment: commentList[index],
                          commentFieldController: _commentFieldController,
                          commentFieldFocusNode: _focusNode,
                        );
                      }
                    : null,
                onDeleteCommentTap: isOwnComment
                    ? () async {
                        final activeCommentKey = _activeCommentKeysNotifier
                            .value[commentList[index].id];

                        if (activeCommentKey == null) {
                          return;
                        }

                        await DeleteCommentUtil(
                          context: context,
                          ref: ref,
                          photoId: widget.photo.id,
                          commentToDelete: commentList[index],
                          targetKey: activeCommentKey,
                        ).handleDeleteEvent();
                      }
                    : null,
                onCommentReported: isOwnComment ? null : _handleCommentReported,
              );
            }, childCount: commentList.length),
          ),
        ],
      ),
    );
  }

  Future<void> _loadMore() async {
    _isFetchingNotifier.value = true;

    CommentListResponse response = await _commentListService.fetch(
      postId: widget.photo.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleCommentListResponse(response);
    _isFetchingNotifier.value = false;
  }

  void _handleCommentListResponse(CommentListResponse response) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;
      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _pageIndex++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    final photoCommentService = ref.read(photoCommentReactiveServiceProvider);

    photoCommentService.prependComments(widget.photo.id, response.data);

    return;
  }

  void _handleCommentReported(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}
