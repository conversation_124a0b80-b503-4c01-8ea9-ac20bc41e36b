// category_detail_screen_list_mode.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/providers/camera_photos_interaction_provider.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/providers/category_photos_interaction_provider.dart';
import 'package:portraitmode/category/providers/category_photos_provider.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CategoryDetailScreenListMode extends ConsumerStatefulWidget {
  const CategoryDetailScreenListMode({
    super.key,
    required this.category,
    this.initialScrollIndex = 0,
  });

  final CategoryData category;
  final int initialScrollIndex;

  @override
  CategoryDetailScreenListModeState createState() =>
      CategoryDetailScreenListModeState();
}

class CategoryDetailScreenListModeState
    extends ConsumerState<CategoryDetailScreenListMode> {
  final _scrollController = ScrollController();
  final PhotoListService photoListService = PhotoListService();

  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  bool _loadMoreEndReached = false;

  final ValueNotifier<bool> _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final photoList = ref.watch(categoryPhotosProvider(widget.category.id));
    const double appBarHeight = LayoutConfig.bottomNavBarHeight - 10;

    return Scaffold(
      appBar: PmAppBar(
        height: appBarHeight,
        titleText: widget.category.name,
        useLogo: false,
        automaticallyImplyLeading: true,
        scrollController: _scrollController,
      ),
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(
            maxWidth: ScreenStyleConfig.maxWidth,
          ),
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _isFetchingNotifier,
              builder: (context, isFetching, child) {
                return ValueListenableBuilder(
                  valueListenable: _blockScrollNotifier,
                  builder: (context, blockScrolling, child) {
                    return ScrollablePositionedList.builder(
                      minCacheExtent: getVerticalScrollCacheExtent(context),
                      physics: blockScrolling
                          ? const NeverScrollableScrollPhysics()
                          : null,
                      itemCount:
                          photoList.length +
                          (isFetching && !_loadMoreEndReached ? 1 : 0),
                      itemBuilder: (BuildContext context, int index) {
                        if (index == photoList.length &&
                            isFetching &&
                            !_loadMoreEndReached) {
                          return Padding(
                            padding: EdgeInsets.only(
                              top: photoList.isEmpty ? 0 : 16.0,
                            ),
                            child: LinearProgressIndicator(
                              color: context.colors.baseColorAlt,
                            ),
                          );
                        }

                        return PhotoListItem(
                          key: ValueKey(photoList[index].id),
                          photo: photoList[index],
                          margin: EdgeInsets.only(
                            top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                          ),
                          isOwnProfile: photoList[index].authorId == _profileId,
                          screenName: 'category_detail_screen',
                          onTwoFingersOn: () {
                            _blockScrollNotifier.value = true;
                          },
                          onTwoFingersOff: () {
                            _blockScrollNotifier.value = false;
                          },
                        );
                      },
                      initialScrollIndex: widget.initialScrollIndex,
                      itemScrollController: ItemScrollController(),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref
        .read(cameraPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.category.slug, 0);

    _loadMoreEndReached = false;

    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    final int loadMoreLastId = ref
        .read(categoryPhotosInteractionProvider.notifier)
        .getLoadMoreLastId(widget.category.slug);

    final isFirstLoad = loadMoreLastId == 0;

    PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      categoryId: widget.category.id,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, isRefresh, isFirstLoad);
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final categoryPhotosReactiveService = ref.read(
      categoryPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        categoryPhotosReactiveService.clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    ref
        .read(cameraPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.category.slug, response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      categoryPhotosReactiveService.replaceAll(
        widget.category.id,
        response.data,
      );
    } else {
      categoryPhotosReactiveService.addItems(widget.category.id, response.data);
    }
  }
}
