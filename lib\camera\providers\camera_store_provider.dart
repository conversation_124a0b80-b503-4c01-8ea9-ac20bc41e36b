// camera_store_provider.dart

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';

/// A highly optimized and memory-efficient notifier for managing cameras by ID.
///
/// Uses `Map<int, CameraData>` for O(1) operations and eliminates the need for
/// index caching and list copying that plagued the previous implementation.
final class CameraStoreNotifier
    extends AutoDisposeNotifier<Map<int, CameraData>> {
  @override
  Map<int, CameraData> build() {
    return <int, CameraData>{};
  }

  /// Creates a new state map to trigger provider updates.
  /// Only creates new map if there are actual changes.
  void _updateState(
    Map<int, CameraData> Function(Map<int, CameraData>) updater,
  ) {
    final newState = updater(Map<int, CameraData>.from(state));
    if (!mapEquals(state, newState)) {
      state = newState;
    }
  }

  /// Gets a camera by ID. Returns null if not found.
  CameraData? getItem(int id) {
    return state[id];
  }

  /// Gets multiple cameras by their IDs.
  /// Returns only the cameras that exist, maintaining the order of requested IDs.
  List<CameraData> getItems(List<int> ids) {
    if (ids.isEmpty) return const [];

    final result = <CameraData>[];
    for (final id in ids) {
      final camera = state[id];
      if (camera != null) {
        result.add(camera);
      }
    }
    return result;
  }

  /// Checks if a camera with the given ID exists.
  bool hasItem(int id) {
    return state.containsKey(id);
  }

  /// Adds a new camera only if it doesn't already exist.
  ///
  /// If [updateIfExists] is true, will update the existing camera.
  void addItem(CameraData newItem, {bool updateIfExists = true}) {
    if (state.containsKey(newItem.id)) {
      if (updateIfExists) updateItem(newItem);
      return;
    }

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Adds multiple cameras efficiently.
  ///
  /// If [updateIfExists] is true, will update existing cameras.
  void addItems(List<CameraData> newItems, {bool updateIfExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          if (updateIfExists) {
            map[item.id] = item;
          }
        } else {
          map[item.id] = item;
        }
      }
      return map;
    });
  }

  /// Updates a single existing camera.
  ///
  /// If [addIfNotExists] is true, will add the camera if it doesn't exist.
  void updateItem(CameraData newItem, {bool addIfNotExists = true}) {
    if (!state.containsKey(newItem.id)) {
      if (addIfNotExists) {
        addItem(newItem, updateIfExists: false);
      }
      return;
    }

    // Only update if the camera has actually changed
    if (state[newItem.id] == newItem) return;

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Updates multiple cameras efficiently.
  ///
  /// If [addIfNotExists] is true, will add cameras that don't exist.
  void updateItems(List<CameraData> newItems, {bool addIfNotExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          map[item.id] = item;
        } else if (addIfNotExists) {
          map[item.id] = item;
        }
      }
      return map;
    });
  }

  /// Removes a camera by ID.
  void removeItem(int id) {
    if (!state.containsKey(id)) return;

    _updateState((map) {
      map.remove(id);
      return map;
    });
  }

  /// Removes multiple cameras by their IDs.
  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;

    // Filter to only IDs that actually exist
    final existingIds = ids.where((id) => state.containsKey(id)).toList();
    if (existingIds.isEmpty) return;

    _updateState((map) {
      for (final id in existingIds) {
        map.remove(id);
      }
      return map;
    });
  }

  /// Removes a camera by slug.
  void removeBySlug(String slug) {
    final cameraToRemove = state.values
        .where((camera) => camera.slug == slug)
        .map((camera) => camera.id)
        .toList();

    if (cameraToRemove.isEmpty) return;

    _updateState((map) {
      for (final cameraId in cameraToRemove) {
        map.remove(cameraId);
      }
      return map;
    });
  }

  /// Replaces the entire store with a new collection of cameras.
  void replaceAll(List<CameraData> newList) {
    final newMap = <int, CameraData>{};
    for (final camera in newList) {
      newMap[camera.id] = camera;
    }
    state = newMap;
  }

  /// Clears all cameras in the store.
  void clear() {
    if (state.isNotEmpty) {
      state = <int, CameraData>{};
    }
  }

  // ------------------------------------------------------------
  // Specialized update methods for single camera fields
  // These are now much more efficient with direct map access
  // ------------------------------------------------------------

  /// Updates a specific field of a camera if it exists.
  void _updateCameraField(
    int cameraId,
    CameraData Function(CameraData) updater,
  ) {
    final currentCamera = state[cameraId];
    if (currentCamera == null) return;

    final updatedCamera = updater(currentCamera);
    if (currentCamera != updatedCamera) {
      _updateState((map) {
        map[cameraId] = updatedCamera;
        return map;
      });
    }
  }

  void setTotalPhotos(int cameraId, int newTotalPhotos) {
    _updateCameraField(
      cameraId,
      (camera) => camera.copyWith(totalPhotos: newTotalPhotos),
    );
  }

  void setLatestPhotoUrl(int cameraId, String newLatestPhotoUrl) {
    _updateCameraField(
      cameraId,
      (camera) => camera.copyWith(latestPhotoUrl: newLatestPhotoUrl),
    );
  }

  void setLatestPhotoId(int cameraId, int newLatestPhotoId) {
    _updateCameraField(
      cameraId,
      (camera) => camera.copyWith(latestPhotoId: newLatestPhotoId),
    );
  }

  void incrementTotalPhotos(int cameraId) {
    _updateCameraField(
      cameraId,
      (camera) => camera.copyWith(totalPhotos: camera.totalPhotos + 1),
    );
  }

  void decrementTotalPhotos(int cameraId) {
    _updateCameraField(
      cameraId,
      (camera) => camera.copyWith(
        totalPhotos: (camera.totalPhotos - 1).clamp(0, double.infinity).toInt(),
      ),
    );
  }

  // ------------------------------------------------------------
  // Utility getters and methods
  // ------------------------------------------------------------

  /// Returns total number of cameras
  int get count => state.length;

  /// Checks if the store is empty
  bool get isEmpty => state.isEmpty;

  /// Checks if the store has at least one camera
  bool get isNotEmpty => state.isNotEmpty;

  /// Returns all camera IDs
  List<int> get allIds => state.keys.toList();

  /// Returns all cameras as a list
  List<CameraData> get allCameras => state.values.toList();

  /// Gets a camera by slug. Returns null if not found.
  CameraData? getCameraBySlug(String slug) {
    return state.values.where((camera) => camera.slug == slug).firstOrNull;
  }

  /// Gets multiple cameras by their slugs.
  List<CameraData> getCamerasBySlugs(List<String> slugs) {
    if (slugs.isEmpty) return const [];

    final slugSet = slugs.toSet();
    return state.values
        .where((camera) => slugSet.contains(camera.slug))
        .toList();
  }
}

/// NotifierProvider for the main camera store
final cameraStoreProvider =
    NotifierProvider.autoDispose<CameraStoreNotifier, Map<int, CameraData>>(
      CameraStoreNotifier.new,
    );

/// A selective provider that only rebuilds when a specific camera changes
final cameraProvider = Provider.family<CameraData?, int>((ref, cameraId) {
  return ref.watch(cameraStoreProvider)[cameraId];
});

/// A selective provider for specific camera fields to minimize rebuilds
final cameraFieldsProvider =
    Provider.family<
      ({
        String? name,
        String? aliasedName,
        String? slug,
        String? url,
        String? latestPhotoUrl,
        int? latestPhotoId,
        int? totalPhotos,
      }),
      int
    >((ref, cameraId) {
      final camera = ref.watch(cameraStoreProvider)[cameraId];
      return (
        name: camera?.name,
        aliasedName: camera?.aliasedName,
        slug: camera?.slug,
        url: camera?.url,
        latestPhotoUrl: camera?.latestPhotoUrl,
        latestPhotoId: camera?.latestPhotoId,
        totalPhotos: camera?.totalPhotos,
      );
    });

/// Provider that returns the current total camera count
final cameraCountProvider = Provider.autoDispose<int>((ref) {
  return ref.watch(cameraStoreProvider.select((cameras) => cameras.length));
});

/// Provider that checks if the store has any cameras
final hasCamerasProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(cameraStoreProvider.select((cameras) => cameras.isNotEmpty));
});

/// Provider that returns all cameras as a list (for UI components that need lists)
final cameraListProvider = Provider.autoDispose<List<CameraData>>((ref) {
  return ref.watch(
    cameraStoreProvider.select((cameras) => cameras.values.toList()),
  );
});

/// Provider that checks if a specific camera ID exists
final hasCameraProvider = Provider.family.autoDispose<bool, int>((
  ref,
  cameraId,
) {
  return ref.watch(
    cameraStoreProvider.select((cameras) => cameras.containsKey(cameraId)),
  );
});

/// Provider that returns a specific camera by ID
final specificCameraProvider = Provider.family.autoDispose<CameraData?, int>((
  ref,
  cameraId,
) {
  return ref.watch(cameraStoreProvider.select((cameras) => cameras[cameraId]));
});

/// Provider that returns a camera by slug
final cameraBySlugProvider = Provider.family.autoDispose<CameraData?, String>((
  ref,
  slug,
) {
  return ref.watch(
    cameraStoreProvider.select(
      (cameras) =>
          cameras.values.where((camera) => camera.slug == slug).firstOrNull,
    ),
  );
});
