import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';

class PhotoViewerScreen extends StatelessWidget {
  final String photoUrl;
  final double height;

  const PhotoViewerScreen({
    super.key,
    required this.photoUrl,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        automaticallyImplyLeading: false,
        leading: BackButton(color: AppColorsCache.light().baseColor),
        backgroundColor: AppColorsCache.light().brandColor,
        useLogo: false,
        actions: const [],
      ),
      backgroundColor: AppColorsCache.light().brandColor,
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          alignment: Alignment.center,
          padding: const EdgeInsets.only(
            bottom: LayoutConfig.bottomNavBarHeight,
          ),
          child: _buildPhoto(),
        ),
      ),
    );
  }

  Widget _buildPhoto() {
    return PhotoView(
      imageProvider: PmNetworkImageProvider(photoUrl).imageProvider,
      minScale: PhotoViewComputedScale.covered,
    );
  }
}
