<manifest
  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="io.portraitmode.app">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
  <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />

  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

  <uses-feature android:name="android.hardware.location.network" android:required="false" />
  <uses-feature android:name="android.hardware.location.gps" android:required="false" />

  <!-- Provide required visibility configuration for API level 30 and above -->
  <queries>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <intent>
      <action android:name="android.intent.action.PROCESS_TEXT" />
      <data android:mimeType="text/plain" />
    </intent>

    <!-- If your app checks for http link support -->
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="http" />
    </intent>

    <!-- If your app checks for https link support -->
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="https" />
    </intent>

    <!-- Explicit apps you know in advance about (for sharing photo): -->
    <package android:name="com.instagram.android" />
    <package android:name="com.zhiliaoapp.musically" />
    <package android:name="com.facebook.katana" />
    <package android:name="com.facebook.orca" />
    <package android:name="org.telegram.messenger" />
    <package android:name="com.whatsapp" />
    <package android:name="com.twitter.android" />

    <!-- allows app to access Facebook app features -->
    <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />

    <!-- allows sharing to Messenger app -->
    <provider android:authorities="com.facebook.orca.provider.PlatformProvider" />
  </queries>

  <application
    android:label="PortraitMode"
    android:name="${applicationName}"
    android:icon="@mipmap/launcher_icon"
    android:enableOnBackInvokedCallback="true">

    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${applicationId}.provider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/provider_paths" />
    </provider>

    <!--
        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider[1004826406517418]"
            android:exported="true" />

        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id" />
        <meta-data android:name="com.facebook.sdk.ClientToken"
    android:value="@string/facebook_client_token" />
        -->

    <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="AIzaSyCeahgZvB1hDXHoT7VH06loY8F6H11cE8w" />

    <activity
      android:name=".MainActivity"
      android:exported="true"
      android:launchMode="singleInstance"
      android:theme="@style/LaunchTheme"
      android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
      android:hardwareAccelerated="true"
      android:windowSoftInputMode="adjustResize">

      <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
      <meta-data
        android:name="io.flutter.embedding.android.NormalTheme"
        android:resource="@style/NormalTheme"
      />

      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>

      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data android:scheme="http" />
        <data android:scheme="https" />

        <data android:host="portraitmode.io" />
      </intent-filter>
    </activity>

    <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
    <meta-data
      android:name="flutterEmbedding"
      android:value="2" />
  </application>
</manifest>
