import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numeral/numeral.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

class FollowersNumber extends ConsumerStatefulWidget {
  const FollowersNumber({
    super.key,
    required this.artistId,
    this.totalFollowers = 0,
    this.numberColor,
    this.textColor,
  });

  final int artistId;
  final int totalFollowers;
  final Color? numberColor;
  final Color? textColor;

  @override
  FollowersNumberState createState() => FollowersNumberState();
}

class FollowersNumberState extends ConsumerState<FollowersNumber> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // pmLog('Building follow icon button of photo id: ${widget.artistId}');

    final int? totalFollowers = ref
        .watch(artistPropsProvider(widget.artistId))
        .totalFollowers;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Numeral(totalFollowers ?? widget.totalFollowers).beautiful,
          style: TextStyle(
            color: widget.numberColor,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          'Followers',
          style: TextStyle(color: widget.textColor, fontSize: 12.0),
        ),
      ],
    );
  }
}
