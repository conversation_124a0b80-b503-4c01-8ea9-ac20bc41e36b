import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/notification/dto/notification_fetch_result.dart';

final class NotificationNotifier
    extends AutoDisposeNotifier<NotificationFetchResult> {
  @override
  NotificationFetchResult build() => NotificationFetchResult();

  void read(List<int> ids) {
    state = state.copyWith(
      totalUnread: state.totalUnread - ids.length,
      totalRead: state.totalRead + ids.length,
      notifications: state.notifications.map((e) {
        if (ids.contains(e.id)) {
          return e.copyWith(isRead: 1);
        }
        return e;
      }).toList(),
    );
  }

  void updateReadStatuses(List<int> ids) {
    state = state.copyWith(
      notifications: state.notifications.map((e) {
        if (ids.contains(e.id)) {
          return e.copyWith(isRead: 1);
        }
        return e;
      }).toList(),
    );
  }

  void updateTotalUnread(int totalUnread) {
    state = state.copyWith(totalUnread: totalUnread);
  }

  void updateTotalRead(int totalRead) {
    state = state.copyWith(totalRead: totalRead);
  }

  void replace(NotificationFetchResult data) {
    state = data;
  }

  void reset() {
    state = NotificationFetchResult();
  }
}

final notificationProvider =
    NotifierProvider.autoDispose<NotificationNotifier, NotificationFetchResult>(
      NotificationNotifier.new,
    );
