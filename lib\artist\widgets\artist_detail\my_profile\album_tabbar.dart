import 'package:flutter/material.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/widgets/add_album_modal.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tabbar_item.dart';

class AlbumTabbar extends StatefulWidget {
  final List<AlbumData> albumList;

  const AlbumTabbar({super.key, required this.albumList});

  @override
  AlbumTabbarState createState() => AlbumTabbarState();
}

class AlbumTabbarState extends State<AlbumTabbar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.colors.lightColor,
        border: Border(
          bottom: BorderSide(color: context.colors.lightColor, width: 1.0),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabBar(
              isScrollable: true,
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.zero,
              labelColor: context.isDarkMode
                  ? context.colors.brandColor
                  : context.colors.brandColorAlt,
              tabs: [
                for (final album in widget.albumList)
                  AlbumTabbarItem(
                    index: widget.albumList.indexOf(album),
                    album: album,
                    isLastItem:
                        widget.albumList.indexOf(album) ==
                        widget.albumList.length - 1,
                    onAlbumDeleted: _handleAlbumDeleted,
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: GestureDetector(
              onTap: () => _handleAddButtonTap(),
              child: Container(
                width: 25.0,
                height: 25.0,
                decoration: BoxDecoration(
                  color: context.isDarkMode
                      ? AppColorsCache.dark().baseColorAlt
                      : AppColorsCache.light().baseColor,
                  borderRadius: const BorderRadius.all(Radius.circular(4.0)),
                ),
                child: Center(
                  child: Icon(
                    Icons.add,
                    color: context.colors.brandColor,
                    size: 16.0,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleAddButtonTap() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder: (BuildContext context) => AddAlbumModal(),
    );
  }

  void _handleAlbumDeleted() {
    if (!mounted) return;
    DefaultTabController.of(context).animateTo(0);
  }
}
