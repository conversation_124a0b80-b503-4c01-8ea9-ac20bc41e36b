import 'package:dio/dio.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';

class RefreshTokenInterceptor extends Interceptor {
  /// List of paths that don't need the access token.
  List<String> noAccessTokenPaths = [
    /// This path is used to get the access token and refresh the access token.
    /// However the refresh access token has no business here (not handled here)
    /// because it uses a different Dio instance.
    '/wp-json/jwt-auth/v1/token',

    /// The token/refresh is not listed here
    /// because it is also using a different Dio instance.
    // '/wp-json/jwt-auth/v1/token/refresh',

    /// These paths are open paths for server debugging.
    '/wp-json/pm/v1/blank',
    '/wp-json/pm/v1/debug/*',

    /// These paths are used for auth purposes.
    '/wp-json/pm/v1/account/check/availability',
    '/wp-json/pm/v1/register',
    '/wp-json/pm/v1/request-password-reset',
    '/wp-json/pm/v1/reset-password',
  ];

  @override
  Future onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    options.contentType = Headers.formUrlEncodedContentType;

    // pmLog('interceptor onRequest: The request path is ${options.path}');

    // Loop through noAccessTokenPaths and check if the current requested url is listed in noAccessTokenPaths.
    // If it is, then don't add the Authorization header to the request.
    for (String noAccessTokenPath in noAccessTokenPaths) {
      if (options.path.contains(noAccessTokenPath)) {
        return handler.next(options);
      }
    }

    String accessToken = LocalAuthService.accessToken ?? '';

    // pmLog('The accessToken on "interceptor onRequest" is: $accessToken');

    // Add the Authorization header to the request
    options.headers['Authorization'] = 'Bearer $accessToken';

    handler.next(options);
  }

  @override
  Future onError(DioException err, ErrorInterceptorHandler handler) async {
    Response<dynamic>? response = err.response;

    if (response == null) {
      return handler.next(err);
    }

    int? statusCode = response.statusCode;

    if (statusCode != 401) {
      return handler.next(err);
    }

    // pmLog('interceptor onError: the response is ${response.toString()}');

    dynamic data = response.data;

    // pmLog('interceptor onError: the data is ${data.toString()}');

    String errorCode = '';

    if (data != null && data is Map) {
      if (data['code'] != null && data['code'] is String) {
        errorCode = data['code'];
      }
    }

    // pmLog('interceptor onError: the errorCode is $errorCode');

    if (errorCode != 'jwt_auth_invalid_token') {
      return handler.next(err);
    }

    // pmLog('interceptor onError: we are going to refresh the access token');

    String refreshToken = LocalAuthService.refreshToken ?? '';

    if (refreshToken.isEmpty) {
      // pmLog('interceptor onError: but the refreshToken value is empty,\nso we will not refresh the token.\nFYI, the response value is: ${response.toString()}');
      // pmLog('----------');

      return handler.next(err);
    }

    // pmLog('interceptor onError: we are going to refresh the access token and the refreshToken value is $refreshToken');

    // If the access token has expired, refresh the access token and retry the request
    DioException? refreshAccessTokenError = await authUtil.refreshAccessToken();

    if (refreshAccessTokenError != null) {
      return handler.next(refreshAccessTokenError);
    }

    RequestOptions newRequestOptions = response.requestOptions;

    // Update the access token in the request headers
    newRequestOptions.headers['Authorization'] =
        'Bearer ${LocalAuthService.accessToken ?? ''}';

    try {
      // Retry the request with the new access token
      Response newResponse = await Dio().fetch(newRequestOptions);

      handler.resolve(newResponse);
    } on DioException catch (e) {
      if (BaseService().errorHasData(e)) {
        // pmLog('Error when retrying the request after refreshing accessToken. It falls into DioException and the error data is: ${e.response?.data}');
      } else {
        // pmLog("Error when retrying the request after refreshing accessToken. It falls into DioException and doesn't have error data. But the response is: ${e.response}");
      }

      if (e.response != null) {
        // pmLog('Error when retrying the request after refreshing accessToken. It falls into DioException and the error data is: ${e.response?.data}');
        return handler.resolve(e.response!);
      }

      handler.next(e);
    }
  }
}
