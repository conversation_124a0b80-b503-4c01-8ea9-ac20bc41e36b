// my_album_tab_content_item.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_response.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/providers/categories_provider.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/notification/utils/notification_util.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class MyAlbumTabContentItem extends ConsumerStatefulWidget {
  final ValueNotifier<bool> loadMoreNotifier;
  final int tabIndex;
  final double containerWidth;
  final int artistId;
  final AlbumData album;

  const MyAlbumTabContentItem({
    super.key,
    required this.loadMoreNotifier,
    required this.tabIndex,
    required this.containerWidth,
    required this.artistId,
    required this.album,
  });

  @override
  MyAlbumTabContentItemState createState() => MyAlbumTabContentItemState();
}

class MyAlbumTabContentItemState extends ConsumerState<MyAlbumTabContentItem>
    with AutomaticKeepAliveClientMixin<MyAlbumTabContentItem> {
  final _categoryListService = CategoryListService();
  final _artistService = ArtistService();
  final _photoListService = PhotoListService();

  late AutoDisposeNotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
  _myAlbumPhotoListProvider;

  final int _loadMorePerPage = LoadMoreConfig.gridItemsPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;
  bool _isLoadingMore = false;
  bool _showingLoadingIndicator = false;

  @override
  bool wantKeepAlive = true;

  @override
  void initState() {
    super.initState();
    _myAlbumPhotoListProvider = getMyAlbumPhotoListProvider(widget.album.slug);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();

      // No need to check against current tab index in post frame callback.
      // If an album is active by tapping the album tab, the current tab index will be the same as the tabIndex.
      // But if an abum is active by swiping, the current tab index will be the previous tab index.
      if (!_showingLoadingIndicator && _isLoadingMore) {
        _showingLoadingIndicator = true;
        AlbumLoadingNotification(true).dispatch(context);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _shouldShowLoadingIndicator(ScrollMetrics metrics) {
    final double remaining =
        metrics.maxScrollExtent - (metrics.pixels + metrics.viewportDimension);

    return remaining <=
        (metrics.viewportDimension * LoadMoreConfig.tresholdPercentage);
  }

  void _onScroll(ScrollMetrics metrics) {
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(metrics.maxScrollExtent);

    // Handle load more when scrolling reaches the trigger point
    if (metrics.pixels >= triggerPoint) {
      _triggerLoadMore();
    }

    if (DefaultTabController.of(context).index == widget.tabIndex) {
      if (_shouldShowLoadingIndicator(metrics) && _isLoadingMore) {
        if (!_showingLoadingIndicator) {
          _showingLoadingIndicator = true;
          AlbumLoadingNotification(true).dispatch(context);
        }
      } else {
        if (_showingLoadingIndicator) {
          _showingLoadingIndicator = false;
          AlbumLoadingNotification(false).dispatch(context);
        }
      }
    }
  }

  bool _canLoadMore() {
    if (!mounted) return false;
    return !_isLoadingMore && !_loadMoreEndReached;
  }

  /// Trigger load more.
  ///
  /// The `_canLoadMore` checking should be in the caller, not here.
  void _triggerLoadMore() async {
    _isLoadingMore = true;

    await _loadMore();

    if (mounted &&
        DefaultTabController.of(context).index == widget.tabIndex &&
        _showingLoadingIndicator) {
      _showingLoadingIndicator = false;
      AlbumLoadingNotification(false).dispatch(context);
    }

    _isLoadingMore = false;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final List<PhotoData> photoList = ref.watch(_myAlbumPhotoListProvider);

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        _onScroll(scrollInfo.metrics);

        return false; // Allow the notification to continue
      },
      child: RefreshIndicator(
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: MasonryGridView.count(
          cacheExtent: getVerticalScrollCacheExtent(context),
          padding: EdgeInsets.all(8.0),
          crossAxisCount: 2,
          mainAxisSpacing: 8.0,
          crossAxisSpacing: 8.0,
          itemCount:
              photoList.length +
              (_isLoadingMore && !_loadMoreEndReached ? 1 : 0),
          itemBuilder: (BuildContext context, int index) {
            if (index >= photoList.length) {
              return const SizedBox.shrink();
            }

            return PhotoMasonryItem(
              key: ValueKey(photoList[index].id),
              index: index,
              photo: photoList[index],
              isOwnProfile: true,
              screenName: 'my_profile',
              onPhotoTap: () => _handlePhotoTap(photoList[index]),
            );
          },
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: 'my_profile'),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    final List<dynamic> responses = await Future.wait([
      _artistService.find(widget.artistId),
      _categoryListService.fetch(),
      _photoListService.fetch(
        albumSlug: widget.album.slug,
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
        artistId: widget.artistId,
      ),
    ]);

    // No need to await for this async call.
    NotificationUtil().fetch(ref, widget.artistId);

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    final ArtistResponse artistResponse = responses[0];

    // Check session ended only on 1 sample.
    if (!artistResponse.success) {
      if (authUtil.errorCodeRequiresLogin(artistResponse.errorCode)) {
        if (mounted) {
          showSessionEndedDialog(context, ref);
        }
      }
    }

    if (artistResponse.success && artistResponse.data != null) {
      final ArtistData artist = artistResponse.data!;

      ref.read(artistStoreProvider.notifier).updateItem(artist);
      ref.read(profileProvider.notifier).replaceFromArtistData(artist);
      ref.read(myAlbumProvider.notifier).replaceAll(artist.albums ?? []);

      await LocalUserService.replaceFromArtistData(artist);
    }

    final CategoryListResponse categoryListResponse = responses[1];

    if (categoryListResponse.success) {
      ref
          .read(categoriesReactiveServiceProvider)
          .replaceAll(categoryListResponse.data);
    }

    final PhotoListResponse photoListResponse = responses[2];

    _handlePhotoListResponse(photoListResponse, true);
  }

  Future<void> _loadMore() async {
    final PhotoListResponse response = await _photoListService.fetch(
      albumSlug: widget.album.slug,
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      artistId: widget.artistId,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, false);
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      widget.loadMoreNotifier.value = false;
      return;
    }

    final bool isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        ref.read(_myAlbumPhotoListProvider.notifier).clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort response.data (photo list) before consuming it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    ref.read(photoStoreProvider.notifier).updateItems(response.data);

    if (isRefresh || isFirstLoad) {
      ref.read(_myAlbumPhotoListProvider.notifier).replaceAll(response.data);
    } else {
      ref.read(_myAlbumPhotoListProvider.notifier).addItems(response.data);
    }
  }
}
