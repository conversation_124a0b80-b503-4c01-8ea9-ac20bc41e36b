import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

class HomeTabbar extends StatefulWidget {
  final TabController? controller;

  const HomeTabbar({super.key, this.controller});

  @override
  HomeTabbarState createState() => HomeTabbarState();
}

class HomeTabbarState extends State<HomeTabbar> {
  final double _xPadding = 20.0;

  @override
  Widget build(BuildContext context) {
    // double tabWidth = containerWidth / 5;
    // pmLog('🪟 Building HomeTabbar');

    return Container(
      padding: EdgeInsets.symmetric(horizontal: _xPadding),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        border: Border(
          bottom: BorderSide(
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
            width: AppConfig.appBarBorderWidth,
          ),
        ),
      ),
      child: TabBar(
        controller: widget.controller,
        isScrollable: false,
        labelPadding: const EdgeInsets.symmetric(
          horizontal: TabbarConfig.labelXPadding,
        ),
        labelColor: context.isDarkMode
            ? context.colors.brandColor
            : context.colors.brandColorAlt,
        tabAlignment: TabAlignment.fill,
        tabs: const [
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.0),
              child: Text('Recent photos'),
            ),
          ),
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.0),
              child: Text('Following'),
            ),
          ),
        ],
      ),
    );
  }
}
