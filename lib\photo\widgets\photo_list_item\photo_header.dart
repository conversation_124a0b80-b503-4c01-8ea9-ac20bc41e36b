import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/artist/widgets/membership_badge.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/common/utils/snackbar_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/misc/vertical_dots.dart';
import 'package:portraitmode/moderation/utils/featured_assignment_util.dart';
import 'package:portraitmode/moderation/utils/potd_assignment_util.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/utils/archive_assignment_util.dart';
import 'package:portraitmode/photo/utils/photo_deletion_util.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/other_photo_bottom_sheet.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/own_photo_bottom_sheet.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class PhotoHeader extends ConsumerStatefulWidget {
  const PhotoHeader({
    super.key,
    required this.photo,
    required this.screenName,
    this.isOwnProfile = false,
  });

  final PhotoData photo;
  final String screenName;
  final bool isOwnProfile;

  @override
  PhotoHeaderState createState() => PhotoHeaderState();
}

class PhotoHeaderState extends ConsumerState<PhotoHeader> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String authorNicename = widget.photo.authorNicename;
    String authorProfileUrl = widget.photo.authorProfileUrl;
    String authorDisplayName = widget.photo.authorDisplayName;
    String authorAvatarUrl = widget.photo.authorAvatarUrl;
    String authorMembershipType = widget.photo.authorMembershipType;

    if (widget.isOwnProfile) {
      ProfileData profile = ref.watch(profileProvider);

      // On first load, the profile provider data is still empty.
      if (profile.nicename.isEmpty) {
        final user = LocalUserService.get();

        authorNicename = user.nicename ?? '';
        authorProfileUrl = user.profileUrl ?? '';
        authorDisplayName = user.displayName ?? '';
        authorAvatarUrl = user.avatarUrl ?? '';
        authorMembershipType = user.membershipType ?? '';
      } else {
        authorNicename = profile.nicename;
        authorProfileUrl = profile.profileUrl;
        authorDisplayName = profile.displayName;
        authorAvatarUrl = profile.avatarUrl;
        authorMembershipType = profile.membershipType;
      }
    }

    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 5.0),
          child: Avatar(
            imageUrl: authorAvatarUrl,
            size: 31.0,
            isOwnAvatar: widget.isOwnProfile,
            onTap: () {
              _handlePhotoTap(
                context: context,
                isOwnProfile: widget.isOwnProfile,
                authorNicename: authorNicename,
                authorProfileUrl: authorProfileUrl,
                authorDisplayName: authorDisplayName,
                authorAvatarUrl: authorAvatarUrl,
                authorMembershipType: authorMembershipType,
              );
            },
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  _handlePhotoTap(
                    context: context,
                    authorNicename: authorNicename,
                    authorProfileUrl: authorProfileUrl,
                    authorDisplayName: authorDisplayName,
                    authorAvatarUrl: authorAvatarUrl,
                    authorMembershipType: authorMembershipType,
                  );
                },
                child: Row(
                  children: [
                    Text(
                      authorDisplayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13.0,
                      ),
                    ),
                    if (widget.photo.authorMembershipType.isNotEmpty)
                      MembershipBadge(
                        membershipType: widget.photo.authorMembershipType,
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 2.0),
              Text(
                widget.photo.address,
                style: const TextStyle(fontSize: 12.0),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: VerticalDots(
            onTap: () {
              if (widget.isOwnProfile) {
                _showOwnPhotoBottomSheet(context);
                return;
              }

              _showOtherPhotoBottomSheet(context);
            },
          ),
        ),
      ],
    );
  }

  void _handlePhotoTap({
    required BuildContext context,
    bool isOwnProfile = false,
    String authorNicename = '',
    String authorProfileUrl = '',
    String authorDisplayName = '',
    String authorAvatarUrl = '',
    String authorMembershipType = '',
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          isOwnProfile: isOwnProfile,
          partialData: ArtistPartialData(
            id: widget.photo.authorId,
            nicename: authorNicename,
            displayName: authorDisplayName,
            profileUrl: authorProfileUrl,
            avatarUrl: authorAvatarUrl,
            membershipType: authorMembershipType,
          ),
        ),
      ),
    );
  }

  void _showOwnPhotoBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      builder: (BuildContext context) {
        return OwnPhotoBottomSheet(
          photo: widget.photo,
          screenName: widget.screenName,
          onPhotoArchiveAssignment:
              (AssignmentActionType actionType, PhotoData photo) {
                _handlePhotoArchiveAssignment(
                  photo: photo,
                  actionType: actionType,
                );
              },
          onDeletePhoto: (PhotoData photo) {
            _handleDeletePhoto(isModeration: false);
          },
          onFeaturedAssignment: _handleFeaturedAssignment,
          onPotdAssignment: _handlePotdAssignment,
        );
      },
    );
  }

  void _startProcessing() {
    //
  }

  void _stopProcessing({VoidCallback? callback}) {
    callback?.call();
  }

  void _showOtherPhotoBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      builder: (BuildContext context) {
        return OtherPhotoBottomSheet(
          photo: widget.photo,
          isOwnPhoto: widget.isOwnProfile,
          screenName: widget.screenName,
          onDeletePhotoByMod: (PhotoData photo) {
            _handleDeletePhoto(isModeration: true);
          },
          onFeaturedAssignment: _handleFeaturedAssignment,
          onPotdAssignment: _handlePotdAssignment,
        );
      },
    );
  }

  void _handlePhotoArchiveAssignment({
    required PhotoData photo,
    required AssignmentActionType actionType,
  }) async {
    ArchiveAssignmentUtil(
      ref: ref,
      photo: widget.photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
      onSuccess: _showSuccessSnackBar,
      onSessionEndedError: _showSessionEndedDialog,
      onError: _showErrorSnackBar,
    ).handleAssignment(actionType: actionType);
  }

  void _handleDeletePhoto({bool isModeration = false}) async {
    PhotoDeletionUtil(
      context: context,
      ref: ref,
      photo: widget.photo,
      isOwnPhoto: widget.isOwnProfile,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleDeletion(isPhotoReportModeration: isModeration);
  }

  void _handleFeaturedAssignment({
    required AssignmentActionType action,
    bool notifyAuthor = false,
  }) async {
    FeaturedAssignmentUtil(
      context: context,
      ref: ref,
      photo: widget.photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleAssignment(action: action, notifyAuthor: notifyAuthor);
  }

  void _handlePotdAssignment({required AssignmentActionType action}) async {
    PotdAssignmentUtil(
      context: context,
      ref: ref,
      photo: widget.photo,
      startLoading: _startProcessing,
      stopLoading: _stopProcessing,
    ).handleAssignment(action: action);
  }

  void _showSuccessSnackBar(String msg) {
    showAppSnackBar(
      context: context,
      message: msg,
      type: AppSnackBarType.success,
    );
  }

  void _showSessionEndedDialog() {
    showSessionEndedDialog(context, ref);
  }

  void _showErrorSnackBar(String msg) {
    showAppSnackBar(context: context, message: msg);
  }
}
