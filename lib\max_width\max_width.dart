import 'package:flutter/material.dart';

class MaxWidth extends StatelessWidget {
  final double maxWidth;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Widget child;

  const MaxWidth({
    super.key,
    required this.maxWidth,
    this.width,
    this.height,
    this.margin,
    this.padding,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: child,
    );
  }
}

class MaxWidthBuilder extends StatelessWidget {
  const MaxWidthBuilder({
    super.key,
    required this.maxWidth,
    this.width,
    this.height,
    this.margin,
    this.padding,
    required this.builder,
  });

  final double maxWidth;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Widget Function(BuildContext, BoxConstraints) builder;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: LayoutBuilder(builder: builder),
    );
  }
}
