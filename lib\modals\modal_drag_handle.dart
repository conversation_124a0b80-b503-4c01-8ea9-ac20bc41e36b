// modal_drag_handle.dart

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

class ModalDragHandle extends StatelessWidget {
  final double height;
  final double width;
  final AlignmentGeometry? alignment;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const ModalDragHandle({
    super.key,
    this.height = BottomSheetConfig.dragHandleHeight,
    this.width = BottomSheetConfig.dragHandleWidth,
    this.alignment,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: alignment ?? Alignment.center,
      margin: margin,
      padding:
          padding ??
          EdgeInsets.only(
            top: BottomSheetConfig.dragHandleMarginTop,
            bottom: BottomSheetConfig.dragHandleMarginBottom,
          ),
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: context.colors.greyColor,
          borderRadius: BorderRadius.circular(height / 2),
        ),
      ),
    );
  }
}
