import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_ids_map_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class CategoryPhotoIdsNotifier extends IdIdsMapNotifier {}

final categoryPhotoIdsProvider =
    NotifierProvider.autoDispose<CategoryPhotoIdsNotifier, Map<int, List<int>>>(
      CategoryPhotoIdsNotifier.new,
    );

final categoryPhotosProvider = Provider.autoDispose
    .family<List<PhotoData>, int>((ref, categoryId) {
      final idsMap = ref.watch(categoryPhotoIdsProvider);
      final store = ref.watch(photoStoreProvider);
      final items = <PhotoData>[];

      final ids = idsMap[categoryId];
      if (ids == null || ids.isEmpty) return items;

      for (final id in ids) {
        final item = store[id];
        if (item != null) {
          items.add(item);
        }
      }

      return items;
    });

/// High-level service for managing category photos's reactivity.
/// This is the recommended way to manage category photos.
final class CategoryPhotosReactiveService {
  const CategoryPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get category photos list for a specific category
  List<PhotoData> getAll(int categoryId) {
    return ref.read(categoryPhotosProvider(categoryId));
  }

  /// Get category photo IDs for a specific category
  List<int> getAllIds(int categoryId) {
    return ref.read(categoryPhotoIdsProvider.notifier).getIds(categoryId) ?? [];
  }

  /// Get all category photo IDs across all categories
  Map<int, List<int>> getAllIdsMap() {
    return ref.read(categoryPhotoIdsProvider);
  }

  /// Add a new category photo (adds to both global store and category photos list)
  void addItem(int categoryId, PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to category photos list
    ref.read(categoryPhotoIdsProvider.notifier).addItem(categoryId, photo.id);
  }

  /// Add multiple category photos
  void addItems(int categoryId, List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to category photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(categoryPhotoIdsProvider.notifier).addItems(categoryId, photoIds);
  }

  /// Remove a category photo from a specific category
  void removeFromCategory(int categoryId, int photoId) {
    // Remove from category photos list
    ref
        .read(categoryPhotoIdsProvider.notifier)
        .removeIdFromKey(categoryId, photoId);
  }

  /// Remove a photo from all categories that contain it
  void removeFromAllCategories(int photoId) {
    ref.read(categoryPhotoIdsProvider.notifier).removeIdFromAllKeys(photoId);
  }

  /// Remove a category entirely
  void removeCategory(int categoryId) {
    // Remove category from category photos list
    ref.read(categoryPhotoIdsProvider.notifier).removeItem(categoryId);
  }

  /// Remove multiple categories
  void removeCategories(List<int> categoryIds) {
    if (categoryIds.isEmpty) return;

    // Remove categories from category photos list
    ref.read(categoryPhotoIdsProvider.notifier).removeItems(categoryIds);
  }

  /// Update a photo in the store (automatically reflects in category photos list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all category photos for a specific category
  void replaceAll(int categoryId, List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace category photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref
        .read(categoryPhotoIdsProvider.notifier)
        .upsertItems(categoryId, photoIds);
  }

  /// Clear all category photos
  void clear() {
    // Clear the category photos list
    ref.read(categoryPhotoIdsProvider.notifier).clear();
  }

  /// Clear photos for a specific category
  void clearCategory(int categoryId) {
    // Clear the category photos list for specific category
    ref.read(categoryPhotoIdsProvider.notifier).removeItem(categoryId);
  }

  /// Get total number of categories
  int get categoryCount => ref.read(categoryPhotoIdsProvider.notifier).length;

  /// Get category photos count for a specific category
  int getCategoryPhotoCount(int categoryId) =>
      ref.read(categoryPhotoIdsProvider.notifier).getIdsCount(categoryId);

  /// Get total photos count across all categories
  int get totalPhotoCount =>
      ref.read(categoryPhotoIdsProvider.notifier).totalIdsCount;

  /// Check if category photos list is empty
  bool get isEmpty => ref.read(categoryPhotoIdsProvider.notifier).isEmpty;

  /// Check if category photos list is not empty
  bool get isNotEmpty => ref.read(categoryPhotoIdsProvider.notifier).isNotEmpty;

  /// Check if a specific category has photos
  bool hasCategory(int categoryId) =>
      ref.read(categoryPhotoIdsProvider.notifier).hasItem(categoryId);

  /// Get all category slugs
  List<int> get allKeys => ref.read(categoryPhotoIdsProvider.notifier).keys;
}

/// Provider for the CategoryPhotosReactiveService.
final categoryPhotosReactiveServiceProvider =
    Provider.autoDispose<CategoryPhotosReactiveService>((ref) {
      return CategoryPhotosReactiveService(ref);
    });
