import 'package:flutter/foundation.dart';

class LikePhotoResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final LikePhotoResponseData? data;

  LikePhotoResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory LikePhotoResponse.fromMap(Map<String, dynamic> map) {
    return LikePhotoResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? LikePhotoResponseData.fromMap(map['data'])
          : null,
    );
  }
}

@immutable
class LikePhotoResponseData {
  final int totalLikes;

  const LikePhotoResponseData({this.totalLikes = 0});

  factory LikePhotoResponseData.fromMap(Map<String, dynamic> map) {
    return LikePhotoResponseData(totalLikes: map['totalLikes'] ?? 0);
  }

  LikePhotoResponseData copyWith({int? totalLikes}) {
    return LikePhotoResponseData(totalLikes: totalLikes ?? this.totalLikes);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LikePhotoResponseData) return false;

    return other.totalLikes == totalLikes;
  }

  @override
  int get hashCode => totalLikes.hashCode;

  @override
  String toString() {
    return '''
LikePhotoResponseData(
  totalLikes: $totalLikes,
)
''';
  }
}
