// latest_photos_provider.dart

import 'package:portraitmode/app/utils/log_util.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/dto/load_more_exception.dart';
import 'package:portraitmode/app/dto/pagination_state.dart';
import 'package:portraitmode/app/providers/load_more_id_list_async_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';

/// LatestPhotoIdsAsyncNotifier focused only on photo-specific logic
final class LatestPhotoIdsAsyncNotifier extends LoadMoreIdListAsyncNotifier {
  late PhotoListService _service;

  @override
  Future<List<int>> performInitialLoad() async {
    _service = PhotoListService();
    return fetchPage(lastId: null, viewedIds: [], isInitialLoad: true);
  }

  @override
  Future<List<int>> fetchPage({
    required int? lastId,
    required List<int> viewedIds,
    bool isInitialLoad = false,
  }) async {
    try {
      final response = await retryOperation(
        () => _service.fetchRecent(
          limit: LoadMoreConfig.itemsPerPage,
          lastId: lastId,
          viewedPhotoIds: viewedIds,
        ),
        context: isInitialLoad ? 'initial_load' : 'pagination',
      );

      if (!response.success) {
        throw LoadMoreException(response.message, response.errorCode);
      }

      final photos = response.data;

      if (photos.isNotEmpty) {
        // Batch add photos to store
        final store = ref.read(photoStoreProvider.notifier);
        store.addItems(photos);
      }

      // Extract IDs efficiently
      return List.generate(photos.length, (i) => photos[i].id, growable: false);
    } catch (error) {
      if (error is LoadMoreException) {
        rethrow;
      }
      throw LoadMoreException(
        'Failed to fetch photos: ${error.toString()}',
        isInitialLoad ? 'INITIAL_LOAD_FAILED' : 'PAGINATION_FAILED',
      );
    }
  }

  @override
  Future<bool> shouldMarkAsReachedEnd(List<int> lastFetchedIds) async {
    // Photo-specific logic: if we got fewer than expected, we've reached the end
    return lastFetchedIds.length < LoadMoreConfig.itemsPerPage;
  }

  @override
  Future<bool> hasNewItems() async {
    if (currentIds.isEmpty) return false;

    try {
      // Fetch just the latest photo to check
      final response = await _service.fetchRecent(
        limit: 1,
        lastId: null,
        viewedPhotoIds: [],
      );

      if (!response.success || response.data.isEmpty) {
        pmLog('Failed to fetch latest photo: ${response.message}');
        return false;
      }

      final latestPhoto = response.data.first;
      return latestPhoto.id != currentIds.first;
    } catch (error) {
      return false; // Assume no new items on error
    }
  }

  // Optional: Override debounce delays if needed for photos
  @override
  Duration getRefreshDebounceDelay() => const Duration(milliseconds: 300);

  @override
  Duration getNewItemsCheckDelay() => const Duration(milliseconds: 500);
}

// Provider instances
final latestPhotoIdsAsyncProvider =
    AsyncNotifierProvider.autoDispose<LatestPhotoIdsAsyncNotifier, List<int>>(
      LatestPhotoIdsAsyncNotifier.new,
    );

// All the derived providers remain exactly the same since the interface is preserved
final latestPhotosAsyncProvider =
    Provider.autoDispose<AsyncValue<List<PhotoData>>>((ref) {
      final idsAsync = ref.watch(latestPhotoIdsAsyncProvider);

      return idsAsync.when(
        data: (ids) {
          if (ids.isEmpty) return const AsyncData(<PhotoData>[]);
          final photos = ref.read(photoStoreProvider.notifier).getItems(ids);

          return AsyncData(photos);
        },
        loading: () => const AsyncLoading(),
        error: (error, stack) => AsyncError(error, stack),
      );
    });

// All other providers remain exactly the same...
final latestPhotosLoadingStateProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(
    latestPhotoIdsAsyncProvider.select((state) => state.isLoading),
  );
});

final latestPhotosErrorProvider = Provider.autoDispose<Object?>((ref) {
  return ref.watch(latestPhotoIdsAsyncProvider.select((state) => state.error));
});

// More efficient pagination state providers
final latestPhotosPaginationStateProvider =
    Provider.autoDispose<PaginationState>((ref) {
      return ref.watch(latestPhotoIdsAsyncProvider.notifier).paginationState;
    });

final canLoadMorePhotosProvider = Provider.autoDispose<bool>((ref) {
  final paginationState = ref.watch(latestPhotosPaginationStateProvider);
  final hasPhotos = ref.watch(hasPhotosProvider);

  return paginationState.canLoadMore && hasPhotos;
});

final isLoadingMorePhotosProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(
    latestPhotosPaginationStateProvider.select((state) => state.isLoadingMore),
  );
});

final isRefreshingPhotosProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(latestPhotoIdsAsyncProvider.notifier).isRefreshing;
});

final isCheckingNewPhotosProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(latestPhotoIdsAsyncProvider.notifier).isCheckingForNew;
});

final loadMorePhotosErrorProvider = Provider.autoDispose<Object?>((ref) {
  return ref.watch(
    latestPhotosPaginationStateProvider.select((state) => state.loadMoreError),
  );
});

final hasLoadMoreErrorProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(
    latestPhotosPaginationStateProvider.select(
      (state) => state.hasLoadMoreError,
    ),
  );
});

// Additional helpful providers for UI optimizations

final photoCountProvider = Provider.autoDispose<int>((ref) {
  return ref.watch(
    latestPhotoIdsAsyncProvider.select(
      (state) => state.maybeWhen(data: (ids) => ids.length, orElse: () => 0),
    ),
  );
});

final hasPhotosProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(photoCountProvider.select((count) => count > 0));
});
