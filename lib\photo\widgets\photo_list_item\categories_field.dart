import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_chip.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/widgets/categories_picker.dart';

class CategoriesField extends StatefulWidget {
  const CategoriesField({super.key});

  @override
  CategoriesFieldState createState() => CategoriesFieldState();
}

class CategoriesFieldState extends State<CategoriesField> {
  final ValueNotifier<List<CategoryData>> _selectedCategoriesNotifier =
      ValueNotifier([]);

  @override
  void dispose() {
    _selectedCategoriesNotifier.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 8.0,
        bottom: 16.0,
        left: ScreenStyleConfig.horizontalPadding,
        right: ScreenStyleConfig.horizontalPadding,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          border: Border.all(color: context.colors.borderColor, width: 1.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => CategoriesPicker(
                      selectedCategories: _selectedCategoriesNotifier.value,
                      onClose: (cats) {
                        FocusScope.of(context).unfocus();
                        _selectedCategoriesNotifier.value = cats;
                      },
                    ),
                  ),
                );
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    Ionicons.add_circle,
                    color: context.colors.accentColor,
                    size: 20.0,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    "Add categories",
                    style: TextStyle(color: context.colors.accentColor),
                  ),
                ],
              ),
            ),
            ValueListenableBuilder(
              valueListenable: _selectedCategoriesNotifier,
              builder: (context, selectedCategories, _) {
                if (selectedCategories.isEmpty) const SizedBox.shrink();

                return Padding(
                  padding: const EdgeInsets.only(
                    left: 4.0,
                    right: 4.0,
                    bottom: 2.0,
                  ),
                  child: Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: selectedCategories
                        .map(
                          (cat) => PmChip(
                            label: Text(cat.name),
                            onDeleted: () {
                              _selectedCategoriesNotifier.value = [
                                ..._selectedCategoriesNotifier.value,
                              ]..remove(cat);
                            },
                          ),
                        )
                        .toList(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
