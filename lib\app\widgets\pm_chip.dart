import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class PmChip extends StatelessWidget {
  final Widget label;
  final TextStyle? labelStyle;
  final Color? backgroundColor;
  final VoidCallback? onDeleted;

  const PmChip({
    super.key,
    required this.label,
    this.labelStyle,
    this.backgroundColor,
    this.onDeleted,
  });

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: label,
      labelStyle: labelStyle,
      elevation: 0.0,
      backgroundColor:
          backgroundColor ??
          (context.isDarkMode
              ? AppColorsCache.dark().baseColorAlt.withAlpha(70)
              : AppColorsCache.light().baseColor),
      // These properties are to override Material 3 styling.
      side: BorderSide.none,
      shape: StadiumBorder(),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
      onDeleted: onDeleted,
    );
  }
}
