// search_screen.dart

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/dto/search_screen_data.dart';
import 'package:portraitmode/app/services/screen_service.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/widgets/system_status.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/blog/widget/blog_post_list_slider.dart';
import 'package:portraitmode/camera/widgets/camera_list_slider.dart';
import 'package:portraitmode/category/widgets/category_list_slider.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/photo/providers/trending_photos_provider.dart';
import 'package:portraitmode/photo/widgets/trending_photo_list_slider.dart';
import 'package:portraitmode/potd/widgets/potd.dart';
import 'package:portraitmode/potd/widgets/potd_list_slider.dart';
import 'package:portraitmode/search/widgets/search_field.dart';
import 'package:portraitmode/search/widgets/search_screen_active.dart';

enum ActiveSearchScreenTab { photos, categories, cameras, artists }

class SearchScreen extends ConsumerStatefulWidget {
  final ValueNotifier<VoidCallback?> refreshNotifier;
  final bool searchOnType;

  const SearchScreen({
    super.key,
    required this.refreshNotifier,
    this.searchOnType = false,
  });

  @override
  SearchScreenState createState() => SearchScreenState();
}

class SearchScreenState extends ConsumerState<SearchScreen> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _searchFieldController = TextEditingController();
  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<SearchScreenData?> _searchScreenDataNotifier =
      ValueNotifier(null);

  String _keyword = '';

  final int _counterInterval = 50;
  final int _acceptedWaitingDuration = 500;
  int _waitingDurationCounter = 0;

  Timer? _intervalCounter;

  @override
  void initState() {
    super.initState();

    widget.refreshNotifier.value = _scrollToTop;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchFieldController.dispose();
    _isFetchingNotifier.dispose();
    _searchScreenDataNotifier.dispose();

    if (_intervalCounter != null) _intervalCounter!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    pmLog('🖥️ Building HomeScreen');

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          FocusScope.of(context).unfocus();
        }
      },
      child: Scaffold(
        primary: false,
        body: SafeArea(
          child: MaxWidth(
            maxWidth: 768.0,
            child: RefreshIndicator(
              key: _refreshIndicatorKey,
              color: context.colors.brandColor,
              elevation: 0.0,
              displacement: 50.0,
              onRefresh: _fetchData,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: <Widget>[
                  PmSliverAppBar(
                    scrollController: _scrollController,
                    title: Center(
                      child: SearchField(
                        controller: _searchFieldController,
                        onChanged: _handleSearchFieldChanged,
                        onSubmitted: _handleSearchSubmit,
                      ),
                    ),
                    actions: [],
                  ),
                  SliverToBoxAdapter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(height: 10.0),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: ScreenStyleConfig.horizontalPadding,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            // or MainAxisAlignment.spaceBetween
                            children: [
                              Expanded(
                                child: _buildActiveSearchScreenTabLink(
                                  'Categories',
                                  ActiveSearchScreenTab.categories,
                                ),
                              ),
                              const SizedBox(width: 8.0),
                              Expanded(
                                child: _buildActiveSearchScreenTabLink(
                                  'Cameras',
                                  ActiveSearchScreenTab.cameras,
                                ),
                              ),
                              const SizedBox(width: 8.0),
                              Expanded(
                                child: _buildActiveSearchScreenTabLink(
                                  'Artists',
                                  ActiveSearchScreenTab.artists,
                                ),
                              ),
                            ],
                          ),
                        ),
                        ValueListenableBuilder(
                          valueListenable: _isFetchingNotifier,
                          builder: (context, isFetching, child) {
                            return ValueListenableBuilder(
                              valueListenable: _searchScreenDataNotifier,
                              builder: (context, searchScreenData, child) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const SizedBox(height: 20.0),
                                    Potd(
                                      photo: searchScreenData?.latestPotd,
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 24.0),
                                    TrendingPhotoListSlider(
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 24.0),
                                    CategoryListSlider(
                                      title: "Browse categories",
                                      titleTextStyle: const TextStyle(
                                        fontSize:
                                            SliderConfig.sliderTitleFontSize,
                                        fontWeight:
                                            SliderConfig.sliderTitleFontWeight,
                                      ),
                                      categoryList:
                                          searchScreenData?.categories ?? [],
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 24.0),
                                    CameraListSlider(
                                      title: "Browse cameras",
                                      titleTextStyle: const TextStyle(
                                        fontSize:
                                            SliderConfig.sliderTitleFontSize,
                                        fontWeight:
                                            SliderConfig.sliderTitleFontWeight,
                                      ),
                                      cameraList:
                                          searchScreenData?.cameras ?? [],
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 24.0),
                                    PotdListSlider(
                                      photoList:
                                          searchScreenData?.potdList ?? [],
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 24.0),
                                    BlogPostListSlider(
                                      postList:
                                          searchScreenData?.blogPosts ?? [],
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 24.0),
                                    SystemStatus(
                                      data: searchScreenData?.systemStatus,
                                      isLoading: isFetching,
                                    ),
                                    const SizedBox(height: 5.0),
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  GestureDetector _buildActiveSearchScreenTabLink(
    String text,
    ActiveSearchScreenTab tab,
  ) {
    return GestureDetector(
      onTap: () => _handleTabLinkTap(tab),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          color: context.colors.baseColorAlt,
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 13.0, fontWeight: FontWeight.w700),
        ),
      ),
    );
  }

  void _scrollToTop() async {
    await _scrollController.animateTo(
      -100.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );

    if (_refreshIndicatorKey.currentState != null) {
      await _refreshIndicatorKey.currentState!.show();
    }
  }

  void _handleTabLinkTap(ActiveSearchScreenTab tab) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            SearchScreenActive(keyword: _keyword, initialIndex: tab.index),
      ),
    );
  }

  void _handleSearchFieldChanged(String keyword) {
    if (keyword == _keyword) return;
    _keyword = keyword;

    if (widget.searchOnType) {
      _resetWaitingDuration();
      _setCounter();
    }
  }

  void _setCounter() {
    if (_intervalCounter != null) return;

    _intervalCounter ??= Timer.periodic(
      Duration(milliseconds: _counterInterval),
      (timer) {
        _setWaitingDuration();
      },
    );
  }

  void _setWaitingDuration() {
    _waitingDurationCounter += _counterInterval;

    // pmLog('waitingDurationCounter: $_waitingDurationCounter');

    if (_waitingDurationCounter >= _acceptedWaitingDuration) {
      _resetWaitingDuration();
      _handleSearchSubmit(_keyword);
    }
  }

  void _resetWaitingDuration() {
    _waitingDurationCounter = 0;

    // pmLog('resetting waiting duration');

    if (_intervalCounter != null) {
      _intervalCounter!.cancel();
      _intervalCounter = null;
    }

    // _waitingDurationCounter = 0;
  }

  void _handleSearchSubmit(String value) {
    _searchFieldController.clear();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchScreenActive(keyword: _keyword),
      ),
    );
  }

  Future<void> _fetchData() async {
    if (_isFetchingNotifier.value) return;
    _isFetchingNotifier.value = true;

    final response = await ScreenService().fetchSearchScreenData();

    if (!mounted) return;

    _searchScreenDataNotifier.value = response.data;
    _isFetchingNotifier.value = false;

    if (response.data != null) {
      if (response.data!.photos.isNotEmpty) {
        final trendingPhotosReactiveService = ref.read(
          trendingPhotosReactiveServiceProvider,
        );

        trendingPhotosReactiveService.replaceAll(response.data!.photos);
      }
    }
  }
}
