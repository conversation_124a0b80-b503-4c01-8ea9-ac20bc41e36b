import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';

final class ProfileNotifier extends AutoDisposeNotifier<ProfileData> {
  @override
  ProfileData build() {
    ref.keepAlive();
    return const ProfileData();
  }

  void setTotalFollowing(int totalFollowing) {
    state = state.copyWith(totalFollowing: totalFollowing);
  }

  dynamic getProp(String prop) {
    return state.toMap()[prop];
  }

  void setProps({
    String? nicename,
    String? email,
    String? role,
    String? website,
    String? profileUrl,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? displayName,
    String? description,
    String? location,
    String? latestPhotoUrl,
    int? totalPhotos,
    String? camera,
    String? focalLength,
    bool? isFollowing,
    int? totalFollowing,
    int? totalFollowers,
    String? membershipType,
  }) {
    final ProfileData newState = state.copyWith(
      nicename: nicename ?? state.nicename,
      email: email ?? state.email,
      role: role ?? state.role,
      website: website ?? state.website,
      profileUrl: profileUrl ?? state.profileUrl,
      avatarUrl: avatarUrl ?? state.avatarUrl,
      firstName: firstName ?? state.firstName,
      lastName: lastName ?? state.lastName,
      displayName: displayName ?? state.displayName,
      description: description ?? state.description,
      location: location ?? state.location,
      latestPhotoUrl: latestPhotoUrl ?? state.latestPhotoUrl,
      totalPhotos: totalPhotos ?? state.totalPhotos,
      camera: camera ?? state.camera,
      focalLength: focalLength ?? state.focalLength,
      isFollowing: isFollowing ?? state.isFollowing,
      totalFollowing: totalFollowing ?? state.totalFollowing,
      totalFollowers: totalFollowers ?? state.totalFollowers,
      membershipType: membershipType ?? state.membershipType,
    );

    if (newState != state) {
      state = newState;
    }
  }

  void replace(ProfileData data) {
    state = data;
  }

  void replaceFromArtistData(ArtistData artist) {
    setProps(
      nicename: artist.nicename,
      email: artist.email,
      role: artist.role,
      website: artist.website,
      profileUrl: artist.profileUrl,
      avatarUrl: artist.avatarUrl,
      firstName: artist.firstName,
      lastName: artist.lastName,
      displayName: artist.displayName,
      description: artist.description,
      location: artist.location,
      latestPhotoUrl: artist.latestPhotoUrl,
      totalPhotos: artist.totalPhotos,
      camera: artist.camera,
      focalLength: artist.focalLength,
      isFollowing: artist.isFollowing,
      totalFollowing: artist.totalFollowing,
      totalFollowers: artist.totalFollowers,
      membershipType: artist.membershipType,
    );
  }

  void reset() {
    state = const ProfileData();
  }
}

final profileProvider =
    NotifierProvider.autoDispose<ProfileNotifier, ProfileData>(
      ProfileNotifier.new,
    );
