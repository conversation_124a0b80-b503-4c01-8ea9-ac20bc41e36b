// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
// Internal packages.
import 'package:portraitmode/artist/http_responses/artist_response.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';

class ArtistService extends BaseService {
  Future<ArtistResponse> find(int artistId) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/artist/${artistId.toString()}',
      );

      return ArtistResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistResponse.fromMap(e.response?.data);
      }

      return ArtistResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return ArtistResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<ArtistResponse> findByNicename(String nicename) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/find-artist/by-nicename/$nicename',
      );

      return ArtistResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistResponse.fromMap(e.response?.data);
      }

      return ArtistResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return ArtistResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> reportArtist({
    required int artistId,
    required String reason,
  }) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/report-artist',
        data: {'artist_id': artistId.toString(), 'reason': reason},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> blockUnblockArtist({
    required int artistId,
    required BlockActionType action,
  }) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/${action.name}-artist',
        data: {'artist_id': artistId.toString()},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}
