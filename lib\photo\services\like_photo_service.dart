import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/photo/http_responses/like_photo_response.dart';

class LikePhotoService extends BaseService {
  Future<LikePhotoResponse> like(int photoId) async {
    return doAction(photoId, 'like');
  }

  Future<LikePhotoResponse> unlike(int photoId) async {
    return doAction(photoId, 'unlike');
  }

  Future<LikePhotoResponse> doAction(int photoId, String actionType) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/$actionType-photo',
        data: {'photo_id': photoId.toString()},
      );

      return LikePhotoResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return LikePhotoResponse.fromMap(e.response?.data);
      }

      return LikePhotoResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return LikePhotoResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}
