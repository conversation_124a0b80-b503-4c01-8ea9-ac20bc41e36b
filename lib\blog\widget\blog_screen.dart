import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/responses/blog_post_fetch_response.dart';
import 'package:portraitmode/blog/services/blog_post_service.dart';
import 'package:portraitmode/blog/widget/blog_post_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class BlogScreen extends ConsumerStatefulWidget {
  const BlogScreen({super.key});

  @override
  BlogScreenState createState() => BlogScreenState();
}

class BlogScreenState extends ConsumerState<BlogScreen> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  final int _loadMorePerPage = 10;
  int _currentPageNumber = 1;
  bool _loadMoreEndReached = false;

  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<List<BlogPostData>> _blogPostListNotifier = ValueNotifier(
    [],
  );

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _blogPostListNotifier.value.clear();

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _blogPostListNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        automaticallyImplyLeading: true,
        useLogo: false,
        titleText: "Blog",
        // actions: const [],
      ),
      body: SafeArea(
        child: Container(
          constraints: BoxConstraints(maxWidth: ScreenStyleConfig.maxWidth),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _isFetchingNotifier,
              builder: (context, isFetching, child) {
                return ValueListenableBuilder(
                  valueListenable: _blogPostListNotifier,
                  builder: (context, blogPostList, child) {
                    return ListView.builder(
                      controller: _scrollController,
                      cacheExtent: getVerticalScrollCacheExtent(context),
                      itemCount:
                          blogPostList.length +
                          (isFetching && !_loadMoreEndReached ? 1 : 0),
                      itemBuilder: (BuildContext context, int index) {
                        if (index == blogPostList.length &&
                            isFetching &&
                            !_loadMoreEndReached) {
                          return Padding(
                            padding: EdgeInsets.only(
                              top: blogPostList.isEmpty ? 0 : 16.0,
                            ),
                            child: LinearProgressIndicator(
                              color: context.colors.baseColorAlt,
                            ),
                          );
                        }

                        final double marginTop = index == 0
                            ? LayoutConfig.contentTopGap
                            : 12.0;

                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: ScreenStyleConfig.horizontalPadding,
                          ),
                          margin: EdgeInsets.only(top: marginTop),
                          child: BlogPostListItem(
                            post: blogPostList[index],
                            borderRadius: PhotoStyleConfig.borderRadius,
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _currentPageNumber = 1;
    _loadMoreEndReached = false;

    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingNotifier.value = true;

    final BlogPostFetchResponse response = await BlogPostService().fetch(
      perPage: _loadMorePerPage,
      page: _currentPageNumber,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(response, isRefresh);
    _isFetchingNotifier.value = false;
  }

  void _handlePhotoListResponse(
    BlogPostFetchResponse response,
    bool isRefresh,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final isFirstLoad = _currentPageNumber == 1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        _blogPostListNotifier.value = [];
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _currentPageNumber++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      _blogPostListNotifier.value = response.data;
    } else {
      _blogPostListNotifier.value = [
        ..._blogPostListNotifier.value,
        ...response.data,
      ];
    }
  }
}
