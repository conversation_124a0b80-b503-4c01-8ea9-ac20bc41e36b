import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/services/feedback_token_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';

class FeedbackRequestUtil {
  final BuildContext context;
  final WidgetRef ref;
  final PhotoData photo;
  final VoidCallback? startLoading;
  final Function({void Function()? callback})? stopLoading;

  FeedbackRequestUtil({
    required this.context,
    required this.ref,
    required this.photo,
    this.startLoading,
    this.stopLoading,
  });

  Future<void> handleRequest() async {
    if (!context.mounted) return;
    startLoading?.call();

    final feedbackTokenService = FeedbackTokenService();

    final response = await feedbackTokenService.submitAssignment(
      photo.id,
      'assign',
    );

    if (!context.mounted) return;

    if (!response.success) {
      stopLoading?.call();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final PhotoData updatedPhoto = photo.copyWith(needsFeedback: true);

    stopLoading?.call(
      callback: () {
        myAlbumPhotoListProviderMap.forEach((
          String albumSlug,
          AutoDisposeNotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
          provider,
        ) {
          ref.read(provider.notifier).updateItem(updatedPhoto);
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              duration: const Duration(seconds: 2),
              content: Text(response.message),
            ),
          );
        }
      },
    );
  }
}
