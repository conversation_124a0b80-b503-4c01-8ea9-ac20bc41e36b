// id_list_async_notifier.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';

/// An abstract notifier to manage a list of integer IDs using [AsyncNotifier].
///
/// Provides caching and efficient update methods to reduce unnecessary state rebuilds,
/// especially useful for pagination, reordering, and batched operations.
///
/// ⚠️ Do not modify unless you have a deep understanding of how it works.
/// This class handles complex state and cache synchronization logic.
abstract class IdListAsyncNotifier extends AutoDisposeAsyncNotifier<List<int>> {
  /// Internal cache to quickly check for existence of IDs.
  final Set<int> _idCache = {};

  /// Subclasses must implement this to define how the initial list is fetched.
  @override
  Future<List<int>> build();

  /// Updates the state and rebuilds the internal cache with the given list.
  void _updateState(List<int> newState) {
    _rebuildIdCache(newState);
    state = AsyncData(newState);
  }

  /// Rebuilds the internal cache from a new list of IDs.
  void _rebuildIdCache(List<int> newState) {
    _idCache
      ..clear()
      ..addAll(newState);
  }

  /// Public method to update both state and cache together.
  ///
  /// Should be used by subclasses when replacing the entire list.
  void updateStateAndCache(List<int> newState) {
    _updateState(newState);
  }

  /// Adds a list of new IDs to the existing state, ignoring duplicates.
  ///
  /// Optimized for paginated or incremental loading.
  void addToState(List<int> newIds) {
    final currentIds = state.value ?? [];

    // Skip if no unique new IDs
    final filtered = newIds.where((id) => !_idCache.contains(id)).toList();
    if (filtered.isEmpty) return;

    final updated = [...currentIds, ...filtered];
    _updateState(updated);
  }

  /// Adds a list of new items with optional descending sort order.
  ///
  /// [reorder] will sort the resulting list in descending order.
  void addItems(List<int> newItems, {bool reorder = false}) {
    final currentIds = state.value ?? [];
    final filtered = newItems.where((id) => !_idCache.contains(id)).toList();
    if (filtered.isEmpty) return;

    final updated = [...currentIds, ...filtered];

    if (reorder) {
      updated.sort((a, b) => b.compareTo(a));
    }

    _updateState(updated);
  }

  /// Adds a single item with optional descending sort order.
  void addItem(int newItem, {bool reorder = false}) {
    if (_idCache.contains(newItem)) return;

    final currentIds = state.value ?? [];
    final updated = [...currentIds, newItem];

    if (reorder) {
      updated.sort((a, b) => b.compareTo(a));
    }

    _updateState(updated);
  }

  /// Removes an item by its ID, if it exists.
  void removeItem(int id) {
    if (!_idCache.contains(id)) return;

    final currentIds = state.value ?? [];
    final updated = currentIds.where((item) => item != id).toList();
    _updateState(updated);
  }

  /// Removes multiple items by a list of IDs.
  ///
  /// Useful for batch operations like multi-delete.
  void removeItems(List<int> idsToRemove) {
    if (idsToRemove.isEmpty) return;

    final idsToRemoveSet = Set<int>.from(idsToRemove);
    final currentIds = state.value ?? [];
    final updated = currentIds
        .where((item) => !idsToRemoveSet.contains(item))
        .toList();

    // Only update if actual change occurred
    if (updated.length != currentIds.length) {
      _updateState(updated);
    }
  }

  /// Replaces the entire list of IDs with a new list.
  ///
  /// Typically used after refresh or re-fetch operations.
  void replaceAll(List<int> newList) {
    _updateState(newList);
  }

  /// Clears all IDs from both the state and cache.
  void clear() {
    _updateState([]);
  }

  /// Returns the index of a given ID in the current list.
  ///
  /// Returns -1 if the ID does not exist.
  int getIndex(int id) {
    final currentIds = state.value ?? [];
    return currentIds.indexOf(id);
  }

  /// Updates the ID at a specific index with a new value.
  ///
  /// Useful for custom reorder logic.
  void updateAtIndex(int index, int newId) {
    final currentIds = state.value ?? [];
    if (index < 0 || index >= currentIds.length) return;

    final updated = [...currentIds];
    updated[index] = newId;
    _updateState(updated);
  }

  /// Moves an item from one index to another within the list.
  ///
  /// Commonly used for drag-and-drop reordering.
  void moveItem(int fromIndex, int toIndex) {
    final currentIds = state.value ?? [];
    if (fromIndex < 0 ||
        fromIndex >= currentIds.length ||
        toIndex < 0 ||
        toIndex >= currentIds.length) {
      return;
    }

    final updated = [...currentIds];
    final item = updated.removeAt(fromIndex);
    updated.insert(toIndex, item);
    _updateState(updated);
  }

  // -------------------------------
  // Utility and cache accessors
  // -------------------------------

  /// Checks if a specific ID exists in the cache.
  bool hasItem(int id) => _idCache.contains(id);

  /// Returns the number of cached IDs.
  int get cachedCount => _idCache.length;

  /// Returns true if the cache is empty.
  bool get isEmpty => _idCache.isEmpty;

  /// Returns true if the cache is not empty.
  bool get isNotEmpty => _idCache.isNotEmpty;

  /// Returns the number of items in the list (same as [cachedCount]).
  ///
  /// This provides O(1) access via the cache.
  int get length => _idCache.length;

  /// Returns true if the cache is in sync with the current state.
  ///
  /// Useful for debugging or validation.
  bool get isCacheInSync {
    final currentIds = state.value;
    return currentIds != null && _idCache.length == currentIds.length;
  }

  /// Returns the current list of IDs from state.
  ///
  /// Returns an empty list if state is null.
  List<int> get currentIds => state.value ?? [];

  /// Returns a snapshot copy of the cache for safe inspection.
  ///
  /// Useful for debugging without exposing the internal reference.
  Set<int> get cacheSnapshot => Set<int>.from(_idCache);
}
