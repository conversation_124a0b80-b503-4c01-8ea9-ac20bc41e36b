import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/http_responses/album_list_response.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle_delegate.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/services/photo_service.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class AlbumAssignmentModal extends ConsumerStatefulWidget {
  const AlbumAssignmentModal({super.key, required this.photo, this.onSaved});

  final PhotoData photo;
  final Function(String)? onSaved;

  @override
  AlbumAssignmentModalState createState() => AlbumAssignmentModalState();
}

class AlbumAssignmentModalState extends ConsumerState<AlbumAssignmentModal> {
  final _photoService = PhotoService();

  final _isLoadingNotifier = ValueNotifier(false);
  late final ValueNotifier<String> _selectedAlbumSlugNotifier;

  @override
  void initState() {
    super.initState();
    _selectedAlbumSlugNotifier = ValueNotifier(widget.photo.album);
  }

  @override
  void dispose() {
    _isLoadingNotifier.dispose();
    _selectedAlbumSlugNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<AlbumData> albums = ref.watch(myAlbumProvider);

    return DraggableScrollableSheet(
      initialChildSize: 0.5,
      minChildSize: 0.25,
      maxChildSize: BottomSheetConfig.maxChildSize,
      expand: false,
      builder: ((context, scrollController) {
        return SafeArea(
          child: Column(
            children: [
              Expanded(
                child: ValueListenableBuilder(
                  valueListenable: _selectedAlbumSlugNotifier,
                  builder: (context, selectedAlbumSlug, child) {
                    return CustomScrollView(
                      controller: scrollController,
                      slivers: [
                        SliverPersistentHeader(
                          pinned: true,
                          delegate: ModalDragHandleDelegate(),
                        ),

                        SliverList(
                          delegate: SliverChildBuilderDelegate((
                            context,
                            index,
                          ) {
                            if (albums[index].slug != 'all-photos') {
                              return ListTile(
                                title: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      albums[index].text,
                                      style: const TextStyle(fontSize: 14.5),
                                    ),
                                    if (selectedAlbumSlug == albums[index].slug)
                                      const SizedBox(width: 5.0),
                                    if (selectedAlbumSlug == albums[index].slug)
                                      Icon(
                                        Ionicons.checkmark_circle_sharp,
                                        color: context.colors.accentColor,
                                        size: 15,
                                      ),
                                  ],
                                ),
                                onTap: () => _handleOnTap(
                                  albums[index].slug,
                                  selectedAlbumSlug,
                                ),
                                // dense: true,
                                visualDensity: const VisualDensity(
                                  vertical: -3.0,
                                ),
                              );
                            }

                            return SizedBox.shrink();
                          }, childCount: albums.length),
                        ),
                      ],
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: ScreenStyleConfig.horizontalPadding,
                  vertical: ScreenStyleConfig.horizontalPadding,
                ),
                child: ValueListenableBuilder(
                  valueListenable: _selectedAlbumSlugNotifier,
                  builder: (context, selectedAlbumSlug, child) {
                    return SubmitButton(
                      buttonText: "Update",
                      width: double.infinity,
                      height: 40.0,
                      fontWeight: FontWeight.w600,
                      onPressed: () async {
                        await _handleOnSubmit(selectedAlbumSlug);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  void _handleOnTap(String targettedAlbumSlug, String selectedAlbumSlug) {
    if (selectedAlbumSlug == targettedAlbumSlug) {
      _selectedAlbumSlugNotifier.value = '';
    } else {
      _selectedAlbumSlugNotifier.value = targettedAlbumSlug;
    }
  }

  Future<void> _handleOnSubmit(String targettedAlbumSlug) async {
    if (_isLoadingNotifier.value) return;

    _isLoadingNotifier.value = true;

    AlbumListResponse response = await _photoService.assignToAlbum(
      photoId: widget.photo.id,
      albumSlug: targettedAlbumSlug,
    );

    if (!mounted) return;

    if (!response.success) {
      _isLoadingNotifier.value = false;

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    // The albums & their counts are from server side.
    ref.read(myAlbumProvider.notifier).replaceAll(response.data);

    // But the photo update is from client side which I think is fine for now.
    PhotoData updatedPhoto = widget.photo.copyWith(album: targettedAlbumSlug);

    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      AutoDisposeNotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
      provider,
    ) {
      if (albumSlug == 'all-photos') {
        ref.read(provider.notifier).updateItem(updatedPhoto);
      } else {
        if (albumSlug == widget.photo.album) {
          ref.read(provider.notifier).removeItem(widget.photo.id);
        } else if (albumSlug == targettedAlbumSlug) {
          ref.read(provider.notifier).addItemThenReorder(updatedPhoto);
        }
      }
    });

    // Only pop if the modal is still open.
    _isLoadingNotifier.value = false;

    Navigator.of(context).pop();
    widget.onSaved?.call(response.message);
  }
}
