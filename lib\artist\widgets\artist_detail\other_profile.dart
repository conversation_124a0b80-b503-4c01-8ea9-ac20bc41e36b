// other_profile.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/providers/blocked_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/album_tabbar.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/bottom_sheets/other_profile_bottom_sheet.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/other_album_tab_content_item.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/utils/photo_provider_util.dart';

class OtherProfile extends ConsumerStatefulWidget {
  const OtherProfile({
    super.key,
    this.useBackButton = true,
    required this.containerWidth,
    required this.artist,
  });

  final bool useBackButton;
  final double containerWidth;
  final ArtistData artist;

  @override
  OtherProfileState createState() => OtherProfileState();
}

class OtherProfileState extends ConsumerState<OtherProfile> {
  final _scrollController = ScrollController();

  final ValueNotifier<bool> _isLoadingMoreNotifier = ValueNotifier(false);
  final _doingBlockUnblockNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _isLoadingMoreNotifier.dispose();
    _doingBlockUnblockNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final artist = ref.watch(artistProvider(widget.artist.id)) ?? widget.artist;
    final List<AlbumData> albumList = artist.albums ?? [];

    pmLog('🖥️ Building OtherProfile');

    return DefaultTabController(
      length: albumList.length,
      child: Scaffold(
        backgroundColor: context.colors.lightColor,
        body: SafeArea(
          child: NestedScrollView(
            controller: _scrollController,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                PmSliverAppBar(
                  scrollController: _scrollController,
                  automaticallyImplyLeading: widget.useBackButton,
                  backgroundColor: context.colors.lightColor,
                  titleText: widget.useBackButton ? '@${artist.nicename}' : '',
                  useLogo: widget.useBackButton ? false : true,
                  floating: false,
                  pinned: true,
                  actions: [
                    ProfileMenuIndicator(
                      onTap: () => _showOtherProfileBottomSheet(artist),
                    ),
                  ],
                ),
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.only(
                      top: LayoutConfig.contentTopGap,
                      right: ScreenStyleConfig.horizontalPadding,
                      bottom: ScreenStyleConfig.verticalPadding + 20.0,
                      left: ScreenStyleConfig.horizontalPadding,
                    ),
                    child: ProfileHeader(artist: artist),
                  ),
                ),
                SliverPersistentHeader(
                  floating: false,
                  pinned: true,
                  delegate: AlbumTabbar(albumList: albumList),
                ),
              ];
            },
            body: Container(
              color: context.colors.scaffoldColor,
              child: Column(
                children: [
                  Expanded(
                    child: NotificationListener<AlbumLoadingNotification>(
                      onNotification: (albumLoadingNotification) {
                        if (albumLoadingNotification.value) {
                          if (!_isLoadingMoreNotifier.value) {
                            pmLog('Showing loading indicator');
                            _isLoadingMoreNotifier.value = true;
                          }
                        } else {
                          if (_isLoadingMoreNotifier.value) {
                            pmLog('Hiding loading indicator');
                            _isLoadingMoreNotifier.value = false;
                          }
                        }

                        return false;
                      },
                      child: TabBarView(
                        children: albumList.map((AlbumData album) {
                          return OtherAlbumTabContentItem(
                            loadMoreNotifier: _isLoadingMoreNotifier,
                            tabIndex: albumList.indexOf(album),
                            artistId: widget.artist.id,
                            album: album,
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  ValueListenableBuilder(
                    valueListenable: _isLoadingMoreNotifier,
                    builder: (context, isLoadingMore, child) {
                      if (isLoadingMore) {
                        return Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            SizedBox(
                              height: 2.7,
                              child: LinearProgressIndicator(
                                color: context.colors.baseColorAlt,
                              ),
                            ),
                          ],
                        );
                      }

                      return SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showOtherProfileBottomSheet(ArtistData artist) {
    showModalBottomSheet(
      context: context,
      isDismissible: !_doingBlockUnblockNotifier.value,
      useSafeArea: true,
      builder: (BuildContext context) {
        return OtherProfileBottomSheet(
          artist: artist.copyWith(
            isBlocked: ref.read(artistPropsProvider(artist.id)).isBlocked,
          ),
          onBlockArtist: () => _blockUnblockArtist(BlockActionType.block),
          onUnblockArtist: () => _blockUnblockArtist(BlockActionType.unblock),
        );
      },
    );
  }

  void _blockUnblockArtist(BlockActionType action) async {
    _doingBlockUnblockNotifier.value = true;

    final artistService = ArtistService();

    final BaseResponse response = await artistService.blockUnblockArtist(
      artistId: widget.artist.id,
      action: action,
    );

    if (!mounted) return;

    _doingBlockUnblockNotifier.value = false;

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    ref
        .read(artistStoreProvider.notifier)
        .setIsBlocked(widget.artist.id, action == BlockActionType.block);

    if (action == BlockActionType.unblock) {
      ref.read(blockedArtistIdsProvider.notifier).removeItem(widget.artist.id);
    } else {
      ref.read(blockedArtistIdsProvider.notifier).addItem(widget.artist.id);
      removePhotoListFromAppByAuthorId(ref, widget.artist.id);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );
    }
  }
}
