import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/utils/content_util.dart';
import 'package:portraitmode/app/widgets/main_screen.dart';
import 'package:portraitmode/auth/dto/auth_data.dart';
import 'package:portraitmode/auth/http_responses/auth_response.dart';
import 'package:portraitmode/auth/services/auth_service.dart';
import 'package:portraitmode/auth/widgets/login_screen.dart';
import 'package:portraitmode/auth/widgets/privacy_policy_link.dart';
import 'package:portraitmode/auth/widgets/register_screen.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/hive/dto/local_auth_data.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class ResetPasswordScreen extends ConsumerStatefulWidget {
  const ResetPasswordScreen({super.key, required this.email});

  final String email;

  @override
  ResetPasswordScreenState createState() => ResetPasswordScreenState();
}

class ResetPasswordScreenState extends ConsumerState<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final authService = AuthService();

  final ValueNotifier<String?> _errorNotifier = ValueNotifier(null);

  late AuthResponse authResponse;

  final resetCodeFieldController = TextEditingController();
  final passwordFieldController = TextEditingController();

  @override
  void dispose() {
    resetCodeFieldController.dispose();
    passwordFieldController.dispose();

    _errorNotifier.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.sizeOf(context).height;

    return Scaffold(
      backgroundColor: context.colors.lightColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Center(
            child: MaxWidthBuilder(
              maxWidth: 768.0,
              height: screenHeight - MediaQuery.paddingOf(context).top,
              builder: (BuildContext context, BoxConstraints constraints) {
                double containerWidth = constraints.maxWidth;

                return Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Form(
                      key: _formKey,
                      child: SizedBox(
                        width: (containerWidth / 100) * 85,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 25.0),
                              child: Image.asset(
                                context.isDarkMode
                                    ? "assets/logo-white.png"
                                    : "assets/logo.png",
                                width: containerWidth - (containerWidth / 3.5),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: Text(
                                "Check your email for the reset code, then enter that code below.",
                                style: TextStyle(
                                  color: context.colors.primarySwatch[400],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: PmTextField(
                                controller: resetCodeFieldController,
                                labelText: "Reset code",
                                validator:
                                    FieldValidators.resetCodeValidator.call,
                                onChanged: (String value) {
                                  _errorNotifier.value = null;
                                },
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 25.0),
                              child: PmTextField(
                                controller: passwordFieldController,
                                labelText: "Password",
                                obscureText: true,
                                validator:
                                    FieldValidators.passwordValidator.call,
                              ),
                            ),
                            SizedBox(
                              height: 50.0,
                              width: double.infinity,
                              child: SubmitButton(
                                buttonText: "Save password",
                                onPressed: onFormSubmit,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 16.0,
                                bottom: 25.0,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                    onTap: onLoginLinkTap,
                                    child: Text(
                                      "Login",
                                      style: TextStyle(
                                        color: context.colors.accentColor,
                                        fontSize: 13.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 3.0),
                                  Text(
                                    "|",
                                    style: TextStyle(
                                      color: context.colors.primarySwatch[400],
                                      fontSize: 13.0,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const SizedBox(width: 3.0),
                                  GestureDetector(
                                    onTap: onRegisterLinkTap,
                                    child: Text(
                                      "Register",
                                      style: TextStyle(
                                        color: context.colors.accentColor,
                                        fontSize: 13.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16.0),
                            ValueListenableBuilder(
                              valueListenable: _errorNotifier,
                              builder: (context, errorMsg, _) {
                                return errorMsg != null
                                    ? Text(
                                        errorMsg,
                                        style: const TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    : const SizedBox.shrink();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: Platform.isIOS ? 40.0 : 20.0,
                      child: PrivacyPolicyLink(),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void onLoginLinkTap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  void onRegisterLinkTap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RegisterScreen()),
    );
  }

  Future<void> onFormSubmit() async {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) {
      return;
    }

    final deviceInfoPlugin = DeviceInfoPlugin();
    String deviceName = 'mobile';

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfoPlugin.androidInfo;
      deviceName = androidInfo.model;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfoPlugin.iosInfo;
      deviceName = iosInfo.utsname.machine;
    }

    deviceName = slugify(deviceName);

    authResponse = await authService.resetPassword(
      resetCode: resetCodeFieldController.text,
      email: widget.email,
      password: passwordFieldController.text,
      device: deviceName,
    );

    if (!mounted) return;

    if (authResponse.success) {
      AuthData data = authResponse.data ?? AuthData();

      await LocalUserService.replace(
        LocalUserData(
          userId: data.id,
          nicename: data.nicename,
          role: data.role,
          displayName: data.displayName,
          profileUrl: data.profileUrl,
          avatarUrl: data.avatarUrl,
          membershipType: data.membershipType,
        ),
      );

      // pmLog('The accessToken after resetting password is: ${data.accessToken}');
      // pmLog('The refreshToken after resetting password is: ${data.refreshToken}');

      await LocalAuthService.replace(
        LocalAuthData(
          isLoggedIn: true,
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
        ),
      );

      ProfileData profileData = ProfileData(
        id: data.id,
        nicename: data.nicename,
        role: data.role,
        email: data.email,
        website: data.website,
        instagram: data.instagram,
        profileUrl: data.profileUrl,
        avatarUrl: data.avatarUrl,
        firstName: data.firstName,
        lastName: data.lastName,
        displayName: data.displayName,
        description: data.description,
        location: data.location,
        latestPhotoUrl: data.latestPhotoUrl,
        totalPhotos: data.totalPhotos,
        camera: data.camera,
        focalLength: data.focalLength,
        isFollowing: data.isFollowing,
        totalFollowing: data.totalFollowing,
        totalFollowers: data.totalFollowers,
        membershipType: data.membershipType,
      );

      ref.read(profileProvider.notifier).replace(profileData);

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }

      return;
    }

    _errorNotifier.value = authResponse.message;
  }
}
