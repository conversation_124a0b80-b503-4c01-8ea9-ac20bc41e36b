// follower_screen.dart
// This screen displays artist feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/follower_artists_provider.dart';
import 'package:portraitmode/artist/services/follow_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/home/<USER>/empty_follower_notice.dart';

class FollowersScreen extends ConsumerStatefulWidget {
  const FollowersScreen({super.key});

  @override
  FollowersScreenState createState() => FollowersScreenState();
}

class FollowersScreenState extends ConsumerState<FollowersScreen> {
  final _scrollController = ScrollController();
  final FollowListService _followListService = FollowListService();
  final int _loadMorePerPage = LoadMoreConfig.smallItemsPerPage;

  bool _loadMoreEndReached = false;
  int? _loadMoreLastId;
  int? _loadMoreLastTotalPhotos;

  final _isFetchingNotifier = ValueNotifier(false);

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<ArtistData> artistList = ref.watch(followerArtistsProvider);

    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        titleText: "Followers",
        automaticallyImplyLeading: true,
        useLogo: false,
        actions: const [],
      ),
      body: SafeArea(
        child: Container(
          constraints: BoxConstraints(maxWidth: ScreenStyleConfig.maxWidth),
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _isFetchingNotifier,
              builder: (context, isFetching, child) {
                if (!isFetching && _loadMoreEndReached && artistList.isEmpty) {
                  return Center(child: const EmptyFollowerNotice());
                }

                return ListView.builder(
                  controller: _scrollController,
                  cacheExtent: getVerticalScrollCacheExtent(context),
                  itemCount:
                      artistList.length +
                      (isFetching && !_loadMoreEndReached ? 1 : 0),
                  itemBuilder: (BuildContext context, int index) {
                    if (index == artistList.length &&
                        isFetching &&
                        !_loadMoreEndReached) {
                      return Padding(
                        padding: EdgeInsets.only(
                          top: artistList.isEmpty ? 0 : 16.0,
                        ),
                        child: LinearProgressIndicator(
                          color: context.colors.baseColorAlt,
                        ),
                      );
                    }

                    double marginTop = index == 0
                        ? LayoutConfig.contentTopGap
                        : 12.0;

                    return Padding(
                      padding: EdgeInsets.only(
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                        top: marginTop,
                      ),
                      child: ArtistListItem(
                        index: index,
                        artist: artistList[index],
                        isFollowingScreen: true,
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;
    _loadMoreLastTotalPhotos = null;
    _loadMoreLastId = null;

    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingNotifier.value = true;

    final ArtistListResponse response = await _followListService.fetchFollowers(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      lastTotalPhotos: _loadMoreLastTotalPhotos,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleArtistListResponse(response, isRefresh);
    _isFetchingNotifier.value = false;
  }

  Future<void> _handleArtistListResponse(
    ArtistListResponse response,
    bool isRefresh,
  ) async {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final followerArtistsReactiveService = ref.read(
      followerArtistsReactiveServiceProvider,
    );

    final isFirstLoad =
        _loadMoreLastId == null ||
        _loadMoreLastId == 0 ||
        _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        followerArtistsReactiveService.clear();
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;
    _loadMoreLastTotalPhotos = response.data.last.totalPhotos;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      followerArtistsReactiveService.replaceAll(response.data);
    } else {
      followerArtistsReactiveService.addItems(response.data);
    }
  }
}
