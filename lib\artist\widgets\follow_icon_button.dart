import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/http_responses/follow_response.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/providers/following_artists_provider.dart';
import 'package:portraitmode/artist/services/follow_artist_service.dart';

class FollowIconButton extends ConsumerStatefulWidget {
  final int artistId;
  final bool isFollowing;
  final Function(FollowActionType action, FollowResponseData? data)?
  onFollowStatusChanged;

  const FollowIconButton({
    super.key,
    required this.artistId,
    this.isFollowing = false,
    this.onFollowStatusChanged,
  });

  @override
  FollowIconButtonState createState() => FollowIconButtonState();
}

class FollowIconButtonState extends ConsumerState<FollowIconButton> {
  final followArtistService = FollowArtistService();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // pmLog('Building follow icon button of photo id: ${widget.artistId}');

    final bool? maybeIsFollowing = ref
        .watch(artistPropsProvider(widget.artistId))
        .isFollowing;

    final bool isFollowing = maybeIsFollowing == true;

    return Row(
      children: [
        IconButton(
          padding: const EdgeInsets.all(0.0),
          constraints: const BoxConstraints(),
          icon: Icon(
            (isFollowing ? Ionicons.person_add : Ionicons.person_add_outline),
            color: (isFollowing
                ? context.colors.accentColor
                : context.colors.brandColorAlt),
          ),
          iconSize: 26.0,
          onPressed: () {
            doAction(
              isFollowing ? FollowActionType.unfollow : FollowActionType.follow,
            );
          },
        ),
      ],
    );
  }

  Future<void> doAction(FollowActionType action) async {
    // Switch the isFollowing state even though the request hasn't been made.
    widget.onFollowStatusChanged?.call(action, null);

    FollowResponse response = action == FollowActionType.follow
        ? await followArtistService.follow(widget.artistId)
        : await followArtistService.unfollow(widget.artistId);

    if (!response.success || response.data == null) {
      // Switch the following state back to the previous state.
      widget.onFollowStatusChanged?.call(
        action == FollowActionType.follow
            ? FollowActionType.unfollow
            : FollowActionType.follow,
        null,
      );

      return;
    }

    if (action == FollowActionType.unfollow) {
      ref.read(followingArtistIdsProvider.notifier).removeItem(widget.artistId);
    } else {
      ref.read(followingArtistIdsProvider.notifier).addItem(widget.artistId);
    }

    widget.onFollowStatusChanged?.call(action, response.data);
  }
}
