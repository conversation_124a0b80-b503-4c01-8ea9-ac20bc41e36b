import 'package:flutter/foundation.dart';

@immutable
final class PhotosSearchData {
  final int loadMoreLastId;
  final bool loadMoreEndReached;

  const PhotosSearchData({
    this.loadMoreLastId = 0,
    this.loadMoreEndReached = false,
  });

  PhotosSearchData copyWith({int? loadMoreLastId, bool? loadMoreEndReached}) {
    return PhotosSearchData(
      loadMoreLastId: loadMoreLastId ?? this.loadMoreLastId,
      loadMoreEndReached: loadMoreEndReached ?? this.loadMoreEndReached,
    );
  }

  factory PhotosSearchData.fromMap(Map<String, dynamic> map) {
    return PhotosSearchData(
      loadMoreLastId: map['loadMoreLastId'],
      loadMoreEndReached: map['loadMoreEndReached'],
    );
  }

  @override
  int get hashCode => Object.hash(loadMoreLastId, loadMoreEndReached);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PhotosSearchData) return false;

    return other.loadMoreLastId == loadMoreLastId &&
        other.loadMoreEndReached == loadMoreEndReached;
  }
}

@immutable
final class CategoriesSearchData {
  final int offset;
  final bool loadMoreEndReached;

  const CategoriesSearchData({
    this.offset = 0,
    this.loadMoreEndReached = false,
  });

  CategoriesSearchData copyWith({int? offset, bool? loadMoreEndReached}) {
    return CategoriesSearchData(
      offset: offset ?? this.offset,
      loadMoreEndReached: loadMoreEndReached ?? this.loadMoreEndReached,
    );
  }

  factory CategoriesSearchData.fromMap(Map<String, dynamic> map) {
    return CategoriesSearchData(
      offset: map['offset'],
      loadMoreEndReached: map['loadMoreEndReached'],
    );
  }

  @override
  int get hashCode => Object.hash(offset, loadMoreEndReached);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CategoriesSearchData) return false;

    return other.offset == offset &&
        other.loadMoreEndReached == loadMoreEndReached;
  }
}

@immutable
final class CamerasSearchData {
  final int? loadMoreLastId;
  final bool loadMoreEndReached;

  const CamerasSearchData({
    this.loadMoreLastId,
    this.loadMoreEndReached = false,
  });

  CamerasSearchData copyWith({int? lastId, bool? loadMoreEndReached}) {
    return CamerasSearchData(
      loadMoreLastId: lastId ?? loadMoreLastId,
      loadMoreEndReached: loadMoreEndReached ?? this.loadMoreEndReached,
    );
  }

  factory CamerasSearchData.fromMap(Map<String, dynamic> map) {
    return CamerasSearchData(
      loadMoreLastId: map['lastId'],
      loadMoreEndReached: map['loadMoreEndReached'],
    );
  }

  @override
  int get hashCode => Object.hash(loadMoreLastId, loadMoreEndReached);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CamerasSearchData) return false;

    return other.loadMoreLastId == loadMoreLastId &&
        other.loadMoreEndReached == loadMoreEndReached;
  }
}

@immutable
final class ArtistsSearchData {
  final int loadMoreLastId;
  final int lastTotalPhotos;
  final bool loadMoreEndReached;

  const ArtistsSearchData({
    this.loadMoreLastId = -1,
    this.lastTotalPhotos = -1,
    this.loadMoreEndReached = false,
  });

  ArtistsSearchData copyWith({
    int? loadMoreLastId,
    int? offset,
    int? lastTotalPhotos,
    bool? loadMoreEndReached,
  }) {
    return ArtistsSearchData(
      loadMoreLastId: loadMoreLastId ?? this.loadMoreLastId,
      lastTotalPhotos: lastTotalPhotos ?? this.lastTotalPhotos,
      loadMoreEndReached: loadMoreEndReached ?? this.loadMoreEndReached,
    );
  }

  factory ArtistsSearchData.fromMap(Map<String, dynamic> map) {
    return ArtistsSearchData(
      loadMoreLastId: map['loadMoreLastId'],
      lastTotalPhotos: map['lastTotalPhotos'],
      loadMoreEndReached: map['loadMoreEndReached'],
    );
  }

  @override
  int get hashCode =>
      Object.hash(loadMoreLastId, lastTotalPhotos, loadMoreEndReached);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ArtistsSearchData) return false;

    return other.loadMoreLastId == loadMoreLastId &&
        other.lastTotalPhotos == lastTotalPhotos &&
        other.loadMoreEndReached == loadMoreEndReached;
  }
}
