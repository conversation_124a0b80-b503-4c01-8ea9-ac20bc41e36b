import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';
import 'package:portraitmode/moderation/widgets/modals/delete_reported_photo_modal.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class ReviewReportedPhotoModal extends StatefulWidget {
  final PhotoData photo;
  final bool isOwnPhoto;
  final Function? onDeletePhoto;
  final Function? onModerationDismiss;
  final Function(int)? onPhotoDeleted;

  const ReviewReportedPhotoModal({
    super.key,
    required this.photo,
    this.isOwnPhoto = false,
    this.onDeletePhoto,
    this.onModerationDismiss,
    this.onPhotoDeleted,
  });

  @override
  ReviewReportedPhotoModalState createState() =>
      ReviewReportedPhotoModalState();
}

class ReviewReportedPhotoModalState extends State<ReviewReportedPhotoModal> {
  @override
  Widget build(BuildContext context) {
    List<Widget> menuItems = [];

    menuItems.addAll([
      ModalListTile(
        title: "Delete and notify the author",
        textColor: context.colors.dangerColor,
        iconColor: context.colors.dangerColor,
        iconData: Ionicons.trash_outline,
        onTap: () {
          Navigator.pop(context);
          _showDeleteReportedPhotoModal();
        },
      ),
      ModalListTile(
        title: "Delete without notification",
        textColor: context.colors.dangerColor,
        iconColor: context.colors.dangerColor,
        iconData: Ionicons.trash_bin_outline,
        onTap: () {
          Navigator.pop(context);
          _showDeletePhotoDialog();
        },
      ),
      ModalListTile(
        title: "Dismiss this report",
        iconData: Ionicons.alert_circle_outline,
        onTap: () {
          Navigator.pop(context);

          if (widget.onModerationDismiss != null) {
            widget.onModerationDismiss!();
          }
        },
      ),
    ]);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: BottomSheetConfig.verticalSpace),
      child: SafeArea(
        child: SizedBox(
          height: BottomSheetConfig.menuItemHeight * menuItems.length,
          child: Column(children: menuItems),
        ),
      ),
    );
  }

  void _showDeletePhotoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Delete photo'),
        content: const Text(
          'Are you sure you want to delete this photo without notifying the author?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              if (widget.onDeletePhoto != null) {
                widget.onDeletePhoto!();
              }
            },
            child: Text(
              'Delete',
              style: TextStyle(color: context.colors.dangerColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteReportedPhotoModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder: (BuildContext context) {
        return DeleteReportedPhotoModal(
          photo: widget.photo,
          isOwnPhoto: widget.isOwnPhoto,
          onPhotoDeleted: widget.onPhotoDeleted,
        );
      },
    );
  }
}
