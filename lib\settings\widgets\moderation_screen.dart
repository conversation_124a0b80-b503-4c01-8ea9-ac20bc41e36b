import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/moderation/widgets/review_reported_photos_screen.dart';
import 'package:portraitmode/settings/widgets/settings_screen/settings_screen_list_tile.dart';

class ModerationScreen extends StatefulWidget {
  const ModerationScreen({super.key});

  @override
  ModerationScreenState createState() => ModerationScreenState();
}

class ModerationScreenState extends State<ModerationScreen> {
  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            PmSliverAppBar(
              scrollController: _scrollController,
              titleText: 'Moderation',
              automaticallyImplyLeading: true,
              useLogo: false,
              actions: const [],
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 3.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: LayoutConfig.contentTopGap),
                    SettingsScreenListTile(
                      title: 'Reported photos',
                      icon: Ionicons.notifications_outline,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const ReviewReportedPhotosScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Suggested to be featured',
                      icon: Ionicons.trophy_outline,
                      onTap: () {
                        // Navigator.push(
                        //   context,
                        //   MaterialPageRoute(
                        //     builder: (context) => const ThemeScreen(),
                        //   ),
                        // );
                      },
                    ),
                    const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Category suggestions',
                      icon: Ionicons.bookmark_outline,
                      onTap: () {
                        // Navigator.push(
                        //   context,
                        //   MaterialPageRoute(
                        //     builder: (context) => const ThemeScreen(),
                        //   ),
                        // );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
