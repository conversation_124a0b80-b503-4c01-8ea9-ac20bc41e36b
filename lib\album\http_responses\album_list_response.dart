import 'package:portraitmode/album/dto/album_data.dart';

class AlbumListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<AlbumData> data;

  AlbumListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory AlbumListResponse.fromMap(Map<String, dynamic> map) {
    return AlbumListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<AlbumData>((data) => AlbumData.fromMap(data))
                .toList()
          : [],
    );
  }
}
