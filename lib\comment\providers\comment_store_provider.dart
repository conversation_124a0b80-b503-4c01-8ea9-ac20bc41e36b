import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';

final class CommentStoreNotifier
    extends AutoDisposeNotifier<Map<int, CommentData>> {
  @override
  Map<int, CommentData> build() {
    return <int, CommentData>{};
  }

  /// Creates a new state map to trigger provider updates.
  /// Only creates new map if there are actual changes.
  void _updateState(
    Map<int, CommentData> Function(Map<int, CommentData>) updater,
  ) {
    final newState = updater(Map<int, CommentData>.from(state));

    if (!mapEquals(state, newState)) {
      state = newState;
    }
  }

  CommentData? getItem(int id) {
    return state[id];
  }

  /// Gets multiple comments by their IDs.
  /// Returns only the comments that exist, maintaining the order of requested IDs.
  List<CommentData> getItems(List<int> ids) {
    if (ids.isEmpty) return const [];

    final result = <CommentData>[];

    for (final id in ids) {
      final photo = state[id];

      if (photo != null) {
        result.add(photo);
      }
    }

    return result;
  }

  bool hasItem(int id) {
    return state.containsKey(id);
  }

  /// Uniquely adds a new item.
  void addItem(
    CommentData newItem, {
    bool updateIfExists = true,
    bool updateParentTotalReplies = false,
  }) {
    if (hasItem(newItem.id)) {
      // Call updateItem instead of manually writing the update code here.
      if (updateIfExists) updateItem(newItem);
      return;
    }

    _updateState((map) {
      map[newItem.id] = newItem;

      final int parentId = newItem.parentId;

      if (parentId != 0 &&
          updateParentTotalReplies &&
          newItem.submissionStatus == CommentSubmissionStatus.submitted) {
        final parent = map[parentId];

        // Update parent's totalReplies.
        if (parent != null) {
          map[parentId] = parent.copyWith(
            totalReplies: parent.totalReplies + 1,
          );
        }
      }

      return map;
    });
  }

  /// Uniquely adds a list of new items.
  ///
  /// Don't utilize `addItem` method to avoid unnecessary checking.
  void addItems(
    List<CommentData> newItems, {
    bool updateIfExists = true,
    bool updateParentTotalReplies = false,
  }) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        final bool itemExists = map.containsKey(item.id);

        if (itemExists) {
          if (updateIfExists) {
            map[item.id] = item;
          }
        } else {
          map[item.id] = item;

          // Update parent's totalReplies for new items only
          if (updateParentTotalReplies &&
              item.parentId != 0 &&
              item.submissionStatus == CommentSubmissionStatus.submitted) {
            final parent = map[item.parentId];

            if (parent != null) {
              map[item.parentId] = parent.copyWith(
                totalReplies: parent.totalReplies + 1,
              );
            }
          }
        }
      }

      return map;
    });
  }

  /// Updates an existing item.
  void updateItem(CommentData newItem, {bool addIfNotExists = true}) {
    if (!state.containsKey(newItem.id)) {
      if (addIfNotExists) {
        // We don't update directly here, we will call addItem instead.
        // This is because we need to also update its parent's totalReplies.
        addItem(newItem, updateIfExists: false);
      }
      return;
    }

    // If the item is the same, do nothing.
    if (state[newItem.id] == newItem) return;

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Updates a list of existing items.
  ///
  /// Doesn't utilize `updateItem` method to avoid unnecessary checking.
  void updateItems(List<CommentData> newItems, {bool addIfNotExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          // Update existing item
          map[item.id] = item;
        } else if (addIfNotExists) {
          // Add new item
          map[item.id] = item;

          final parentId = item.parentId;

          // Update parent's totalReplies for new items
          if (parentId != 0 &&
              item.submissionStatus == CommentSubmissionStatus.submitted) {
            final parent = map[parentId];
            if (parent != null) {
              map[parentId] = parent.copyWith(
                totalReplies: parent.totalReplies + 1,
              );
            }
          }
        }
      }

      return map;
    });
  }

  void removeItem(int id, {bool updateParentTotalReplies = false}) {
    if (!state.containsKey(id)) return;
    final currentItem = getItem(id);

    _updateState((map) {
      if (currentItem != null &&
          updateParentTotalReplies &&
          currentItem.parentId != 0 &&
          currentItem.submissionStatus == CommentSubmissionStatus.submitted) {
        final parent = map[currentItem.parentId];

        // Update parent's totalReplies.
        if (parent != null) {
          final int updatedTotalReplies = parent.totalReplies - 1;

          map[currentItem.parentId] = parent.copyWith(
            totalReplies: updatedTotalReplies < 0 ? 0 : updatedTotalReplies,
          );
        }
      }

      map.remove(id);
      return map;
    });
  }

  void removeByAuthorId(int authorId, {bool updateParentTotalReplies = false}) {
    _updateState((map) {
      // Find items to remove
      final itemsToRemove = map.values
          .where((item) => item.authorId == authorId)
          .toList();

      if (itemsToRemove.isEmpty) return map;

      // Update parent's totalReplies first
      if (updateParentTotalReplies) {
        for (final item in itemsToRemove) {
          if (item.parentId != 0 &&
              item.submissionStatus == CommentSubmissionStatus.submitted) {
            final parent = map[item.parentId];
            if (parent != null) {
              final updatedTotalReplies = parent.totalReplies - 1;
              map[item.parentId] = parent.copyWith(
                totalReplies: updatedTotalReplies < 0 ? 0 : updatedTotalReplies,
              );
            }
          }
        }
      }

      // Remove the items
      for (final item in itemsToRemove) {
        map.remove(item.id);
      }

      return map;
    });
  }

  void removeByPhotoId(int photoId) {
    // We don't need to update their parent's totalReplies because
    // their parents are also part of the photo that is going to be deleted.
    _updateState((map) {
      map.removeWhere((key, value) => value.postId == photoId);
      return map;
    });
  }

  void replaceAll(List<CommentData> newList) {
    final newMap = <int, CommentData>{};
    for (final comment in newList) {
      newMap[comment.id] = comment;
    }
    state = newMap;
  }

  void clear() {
    if (state.isNotEmpty) {
      state = <int, CommentData>{};
    }
  }

  // ------------------------------------------------------------
  // Additional methods tailored for comment_store_provider
  // ------------------------------------------------------------

  void setSubmissionStatus(
    int commentId,
    CommentSubmissionStatus submissionStatus,
  ) {
    final comment = state[commentId];
    if (comment == null) return;

    _updateState((map) {
      map[commentId] = comment.copyWith(submissionStatus: submissionStatus);
      return map;
    });
  }

  void setIsLiked(int commentId, bool isLiked) {
    final comment = state[commentId];
    if (comment == null) return;

    _updateState((map) {
      map[commentId] = comment.copyWith(isLiked: isLiked);
      return map;
    });
  }

  void setTotalLikes(int commentId, int newTotalLikes) {
    final comment = state[commentId];
    if (comment == null) return;

    _updateState((map) {
      map[commentId] = comment.copyWith(totalLikes: newTotalLikes);
      return map;
    });
  }

  void incrementTotalLikes(int commentId) {
    final comment = state[commentId];
    if (comment == null) return;

    _updateState((map) {
      map[commentId] = comment.copyWith(totalLikes: comment.totalLikes + 1);
      return map;
    });
  }

  void decrementTotalLikes(int commentId) {
    final comment = state[commentId];
    if (comment == null) return;

    final int updatedTotalLikes = comment.totalLikes - 1;

    _updateState((map) {
      map[commentId] = comment.copyWith(
        totalLikes: updatedTotalLikes < 0 ? 0 : updatedTotalLikes,
      );
      return map;
    });
  }

  void setTotalReplies(int commentId, int newTotalReplies) {
    final comment = state[commentId];
    if (comment == null) return;

    _updateState((map) {
      map[commentId] = comment.copyWith(totalReplies: newTotalReplies);
      return map;
    });
  }

  void updateProps({
    required int commentId,
    bool? isLiked,
    int? totalLikes,
    int? totalReplies,
  }) {
    _updateState((map) {
      final comment = map[commentId];
      if (comment == null) return map;

      map[commentId] = comment.copyWith(
        isLiked: isLiked ?? comment.isLiked,
        totalLikes: totalLikes ?? comment.totalLikes,
        totalReplies: totalReplies ?? comment.totalReplies,
      );

      return map;
    });
  }

  CommentData? getTopLevelParent(int commentId) {
    CommentData? current = getItem(commentId);

    while (current != null && current.parentId != 0) {
      current = getItem(current.parentId);
    }

    return current;
  }
}

final commentStoreProvider =
    NotifierProvider.autoDispose<CommentStoreNotifier, Map<int, CommentData>>(
      CommentStoreNotifier.new,
    );

/// A selective provider that only rebuilds when a specific comment changes
final commentProvider = Provider.family.autoDispose<CommentData?, int>((
  ref,
  commentId,
) {
  ref.keepAlive();

  return ref.watch(
    commentStoreProvider.select((comments) => comments[commentId]),
  );
});

final commentPropsProvider = Provider.family
    .autoDispose<({bool? isLiked, int? totalLikes, int? totalReplies}), int>((
      ref,
      commentId,
    ) {
      final comment = ref.watch(
        commentStoreProvider.select((comments) => comments[commentId]),
      );

      return (
        isLiked: comment?.isLiked,
        totalLikes: comment?.totalLikes,
        totalReplies: comment?.totalReplies,
      );
    });

/// Provider that return replies of a single comment.
/// Detected by `parentId`.
final repliesProvider = Provider.family.autoDispose<List<CommentData>, int>((
  ref,
  parentId,
) {
  ref.keepAlive();

  final comments = ref.watch(commentStoreProvider);

  return comments.values
      .where((comment) => comment.parentId == parentId)
      .toList();
});
