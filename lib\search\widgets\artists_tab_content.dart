// artists_tab_content.dart
// This screen displays artist feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/misc.dart';
import 'package:portraitmode/search/providers/search_artists_data_provider.dart';
import 'package:portraitmode/search/providers/search_artists_provider.dart';

class ArtistsTabContent extends ConsumerStatefulWidget {
  const ArtistsTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final ArtistsSearchData searchData;
  final String keyword;
  final List<ArtistData> dataList;

  @override
  ArtistsTabContentState createState() => ArtistsTabContentState();
}

class ArtistsTabContentState extends ConsumerState<ArtistsTabContent>
    with AutomaticKeepAliveClientMixin<ArtistsTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final _artistListService = ArtistListService();
  final int _loadMorePerPage = LoadMoreConfig.smallItemsPerPage;

  bool _fetchingData = false;
  bool _doingRefresh = false;
  bool _showingLoadingIndicator = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _loadMore(false);

      // No need to check against current tab index in post frame callback.
      // If an album is active by tapping the album tab, the current tab index will be the same as the tabIndex.
      // But if an abum is active by swiping, the current tab index will be the previous tab index.
      if (!_showingLoadingIndicator && _fetchingData) {
        _showingLoadingIndicator = true;
        SearchLoadingNotification(true).dispatch(context);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_fetchingData && !widget.searchData.loadMoreEndReached;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            final double triggerPoint = _getLoadMoreTriggerPoint(
              scrollInfo.metrics.maxScrollExtent,
            );

            // Handle load more when scrolling reaches the trigger point
            if (scrollInfo.metrics.pixels >= triggerPoint) {
              if (_canLoadMore()) _loadMore(false);
            }

            if (DefaultTabController.of(context).index == 3) {
              if (scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent - 50 &&
                  _fetchingData &&
                  !_doingRefresh) {
                if (!_showingLoadingIndicator) {
                  _showingLoadingIndicator = true;
                  SearchLoadingNotification(true).dispatch(context);
                }
              } else {
                if (_showingLoadingIndicator) {
                  _showingLoadingIndicator = false;
                  SearchLoadingNotification(false).dispatch(context);
                }
              }
            }

            return false;
          },
          child: ListView.builder(
            cacheExtent: getVerticalScrollCacheExtent(context),
            itemCount: widget.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              final int artistId = widget.dataList[index].id;

              return Padding(
                key: ValueKey(artistId),
                padding: EdgeInsets.only(
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                  top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                ),
                child: ArtistListItem(
                  index: index,
                  artist: widget.dataList[index],
                  isOwnProfile: artistId == _profileId,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  bool _isFirstLoad() {
    return widget.searchData.loadMoreLastId == -1 ||
        widget.searchData.loadMoreLastId == 0;
  }

  Future<void> _handleRefresh() async {
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _doingRefresh = isRefresh;
    _fetchingData = true;

    pmLog('ArtistsTabContentState: _loadMore()');
    // pmLog('The artists search keyword is: "${widget.keyword}"');

    late ArtistListResponse response;

    if (widget.keyword.isNotEmpty) {
      response = await _artistListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: isRefresh ? null : widget.searchData.loadMoreLastId,
        lastTotalPhotos: isRefresh ? null : widget.searchData.lastTotalPhotos,
      );
    } else {
      response = await _artistListService.fetch(
        limit: _loadMorePerPage,
        lastId: isRefresh ? null : widget.searchData.loadMoreLastId,
        lastTotalPhotos: isRefresh ? null : widget.searchData.lastTotalPhotos,
      );
    }

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleArtistListResponse(response, isRefresh);

    _fetchingData = false;
    _doingRefresh = false;

    if (DefaultTabController.of(context).index == 3 &&
        _showingLoadingIndicator) {
      _showingLoadingIndicator = false;
      SearchLoadingNotification(false).dispatch(context);
    }
  }

  void _handleArtistListResponse(ArtistListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchArtistsReactiveService = ref.read(
      searchArtistsReactiveServiceProvider,
    );

    final bool isFirstLoad = _isFirstLoad();

    if (response.data.isEmpty) {
      if (isRefresh || isFirstLoad) {
        searchArtistsReactiveService.clear();
      }

      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(false);
    }

    ref
        .read(artistStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    ref
        .read(searchArtistsDataProvider.notifier)
        .updateSomeValues(
          loadMoreLastId: response.data.last.id,
          lastTotalPhotos: response.data.last.totalPhotos,
        );

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      searchArtistsReactiveService.replaceAll(response.data);
    } else {
      searchArtistsReactiveService.addItems(response.data);
    }
  }
}
