import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/dto/app_state.dart';

final class AppStateNotifier extends AutoDisposeNotifier<AppState> {
  @override
  AppState build() => AppState();

  void setBehaveNewlyOpened(bool value) {
    state = state.copyWith(behaveNewlyOpened: value);
  }

  bool behaveNewlyOpened() {
    return state.behaveNewlyOpened;
  }
}

final appStateProvider =
    NotifierProvider.autoDispose<AppStateNotifier, AppState>(
      AppStateNotifier.new,
    );
