import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/auth/http_responses/auth_response.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/auth/widgets/login_screen.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

class AuthService extends BaseService {
  Future<AuthResponse> login({
    required String username,
    required String password,
    String device = 'mobile',
  }) async {
    try {
      final response = await http.post(
        URL.baseTokenUrl,
        data: {'username': username, 'password': password, 'device': device},
      );

      AuthResponse authResponse = AuthResponse.fromMap(response.data);

      if (authResponse.success) {
        String refreshToken = authUtil.readRefreshTokenCookie(response);
        // pmLog('The refreshToken after login success is: $refreshToken');
        response.data['data']['refreshToken'] = refreshToken;
        // pmLog('The response data after refreshToken assignment after login success is: ${response.data.toString()}');
        authResponse = AuthResponse.fromMap(response.data);
      }

      return authResponse;
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AuthResponse.fromMap(e.response?.data);
      }

      return AuthResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return AuthResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<AuthResponse> register({
    required String username,
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String location,
    double? locationLat,
    double? locationLng,
    String device = 'mobile',
  }) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/register',
        data: {
          'platform': Platform.operatingSystem,
          'username': username,
          'email': email,
          'password': password,
          'first_name': firstName,
          'last_name': lastName,
          'location': location,
          'location_lat': locationLat != null ? locationLat.toString() : '',
          'location_lng': locationLng != null ? locationLng.toString() : '',
        },
      );

      AuthResponse authResponse = AuthResponse.fromMap(response.data);

      if (authResponse.success) {
        String refreshToken = authUtil.readRefreshTokenCookie(response);
        // pmLog('The refreshToken after register success is: $refreshToken');
        response.data['data']['refreshToken'] = refreshToken;
        // pmLog('The response data after refreshToken assignment after register success is: ${response.data.toString()}');
        authResponse = AuthResponse.fromMap(response.data);
      }

      return authResponse;
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AuthResponse.fromMap(e.response?.data);
      }

      return AuthResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return AuthResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<RequestPasswordResetResponse> requestPasswordReset(
    String email,
  ) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/request-password-reset',
        data: {'email': email},
      );

      return RequestPasswordResetResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return RequestPasswordResetResponse.fromMap(e.response?.data);
      }

      return RequestPasswordResetResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return RequestPasswordResetResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<AuthResponse> resetPassword({
    required String resetCode,
    required String email,
    required String password,
    String device = 'mobile',
  }) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/reset-password',
        data: {'reset_code': resetCode, 'email': email, 'password': password},
      );

      AuthResponse authResponse = AuthResponse.fromMap(response.data);

      if (authResponse.success) {
        String refreshToken = authUtil.readRefreshTokenCookie(response);
        // pmLog('The refreshToken after resetPassword success is: $refreshToken');
        response.data['data']['refreshToken'] = refreshToken;
        // pmLog('The response data after refreshToken assignment after resetPassword success is: ${response.data.toString()}');
        authResponse = AuthResponse.fromMap(response.data);
      }

      return authResponse;
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AuthResponse.fromMap(e.response?.data);
      }

      return AuthResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return AuthResponse(success: false, message: "Something went wrong.");
    }
  }

  static Future<void> logout(BuildContext context, WidgetRef ref) async {
    await LocalAuthService.destroy();

    ref.read(photoStoreProvider.notifier).clear();

    if (context.mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        ((route) => false),
      );
    }
  }

  Future<BaseResponse> deleteAccount(String email) async {
    try {
      final response = await http.delete('${URL.baseApiUrl}/accounts/$email');

      BaseResponse deletionResponse = BaseResponse.fromMap(response.data);

      return deletionResponse;
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}
