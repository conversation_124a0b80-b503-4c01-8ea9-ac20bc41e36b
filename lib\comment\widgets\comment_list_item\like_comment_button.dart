import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/comment/http_responses/like_comment_response.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/services/like_comment_service.dart';

final class LikeCommentButton extends ConsumerWidget {
  final int commentId;
  final bool isLiked;
  final int totalLikes;

  const LikeCommentButton({
    super.key,
    required this.commentId,
    this.isLiked = false,
    this.totalLikes = 0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // pmLog(
    //   '🛠️ Building LikeCommentButton of comment "$commentId" with isLiked satus: $isLiked and totalLikes: $totalLikes',
    // );

    return Row(
      children: [
        Text(
          (totalLikes > 0 ? totalLikes.toString() : ''),
          style: TextStyle(fontSize: 11.0, color: context.colors.timeColor),
        ),
        const SizedBox(width: 3.0),
        IconButton(
          padding: const EdgeInsets.all(0.0),
          icon: Icon((isLiked ? Ionicons.heart : Ionicons.heart_outline)),
          iconSize: 18.0,
          color: isLiked
              ? context.colors.accentColor
              : context.colors.contentLighterColor,
          onPressed: () {
            doAction(
              context,
              ref,
              actionType: isLiked ? 'unlike' : 'like',
              currentIsLiked: isLiked,
              currentTotalLikes: totalLikes,
            );
          },
        ),
      ],
    );
  }

  void doAction(
    BuildContext context,
    WidgetRef ref, {
    required String actionType,
    required bool currentIsLiked,
    required int currentTotalLikes,
  }) async {
    if (actionType == 'like' && currentIsLiked) return;
    if (actionType == 'unlike' && !currentIsLiked) return;

    // Switch the isLiked status & total ikes even though the request hasn't been made.
    ref
        .read(commentStoreProvider.notifier)
        .updateProps(
          commentId: commentId,
          isLiked: actionType == 'like',
          totalLikes: actionType == 'like'
              ? currentTotalLikes + 1
              : currentTotalLikes - 1,
        );

    final likeCommentService = LikeCommentService();

    LikeCommentResponse response = actionType == 'like'
        ? await likeCommentService.like(commentId)
        : await likeCommentService.unlike(commentId);

    if (!response.success || response.data == null) {
      // Switch the isLiked status & totalLikes back to the previous state.
      ref
          .read(commentStoreProvider.notifier)
          .updateProps(
            commentId: commentId,
            isLiked: actionType != 'like',
            totalLikes: actionType != 'like'
                ? currentTotalLikes + 1
                : currentTotalLikes - 1,
          );

      return;
    }

    // Replace with the real data from http response.
    ref
        .read(commentStoreProvider.notifier)
        .updateProps(
          commentId: commentId,
          isLiked: response.data!.isLiked,
          totalLikes: response.data!.totalLikes,
        );
  }
}
