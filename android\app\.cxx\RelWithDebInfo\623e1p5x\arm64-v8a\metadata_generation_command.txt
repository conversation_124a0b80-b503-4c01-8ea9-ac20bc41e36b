                        -HC:\Users\<USER>\fvm\versions\stable\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON><PERSON>OID_PLATFORM=android-23
-D<PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\29.0.13113456
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\29.0.13113456
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\29.0.13113456\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\flutter\portraitmode\android\app\build\intermediates\cxx\RelWithDebInfo\623e1p5x\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\flutter\portraitmode\android\app\build\intermediates\cxx\RelWithDebInfo\623e1p5x\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\flutter\portraitmode\android\app\.cxx\RelWithDebInfo\623e1p5x\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2