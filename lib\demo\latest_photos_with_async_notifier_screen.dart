// latest_photo_list_screen.dart

import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/photo/providers/latest_photos_async_provider.dart';

/// This LatestPhotosWithAsyncNotifierScreen is just a demo screen
/// to demonstrate the use of LatestPhotoIdsAsyncNotifier that extends
/// LoadMoreIdListAsyncNotifier class.
///
/// This screen demonstrate all features such as load more, refresh, etc.
class LatestPhotosWithAsyncNotifierScreen extends ConsumerStatefulWidget {
  const LatestPhotosWithAsyncNotifierScreen({super.key});

  @override
  ConsumerState<LatestPhotosWithAsyncNotifierScreen> createState() =>
      _LatestPhotoListScreenState();
}

class _LatestPhotoListScreenState
    extends ConsumerState<LatestPhotosWithAsyncNotifierScreen>
    with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  Timer? _checkNewTimer;
  int? _lastTopPhotoId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController.addListener(_onScroll);

    // Initial snapshot of the top photo ID for comparison
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final photos = ref
          .read(latestPhotosAsyncProvider)
          .maybeWhen(data: (data) => data, orElse: () => []);
      if (photos.isNotEmpty) {
        _lastTopPhotoId = photos.first.id;
      }
    });

    // Start periodic new photo checker
    _checkNewTimer = Timer.periodic(
      const Duration(seconds: 60),
      (_) => _checkAndNotifyNewPhotos(),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _checkNewTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _onScroll() {
    // Use the optimized provider to check if we can load more
    final canLoadMore = ref.read(canLoadMorePhotosProvider);
    final isLoadingMore = ref.read(isLoadingMorePhotosProvider);

    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 300 &&
        canLoadMore &&
        !isLoadingMore) {
      ref.read(latestPhotoIdsAsyncProvider.notifier).loadMore();
    }
  }

  Future<void> _onRefresh() async {
    await ref.read(latestPhotoIdsAsyncProvider.notifier).refresh();
  }

  /// Background check and show SnackBar if new photos are found
  Future<void> _checkAndNotifyNewPhotos() async {
    final notifier = ref.read(latestPhotoIdsAsyncProvider.notifier);
    final prevTopId = _lastTopPhotoId;

    try {
      // Fixed: Use the correct method name
      await notifier.checkForNewItems();

      final photos = ref
          .read(latestPhotosAsyncProvider)
          .maybeWhen(data: (data) => data, orElse: () => []);

      if (photos.isNotEmpty && photos.first.id != prevTopId) {
        _lastTopPhotoId = photos.first.id;

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('New photos available'),
              action: SnackBarAction(
                label: 'Refresh',
                onPressed: () {
                  ref.read(latestPhotoIdsAsyncProvider.notifier).refresh();
                },
              ),
              duration: const Duration(seconds: 6),
            ),
          );
        }
      }
    } catch (_) {
      // Silent fail — optional: log or show debug snackbar
    }
  }

  @override
  Widget build(BuildContext context) {
    final photoListAsync = ref.watch(latestPhotosAsyncProvider);
    final isLoadingMore = ref.watch(isLoadingMorePhotosProvider);
    final loadMoreError = ref.watch(loadMorePhotosErrorProvider);
    final isRefreshing = ref.watch(isRefreshingPhotosProvider);
    final isCheckingNew = ref.watch(isCheckingNewPhotosProvider);
    final hasPhotos = ref.watch(hasPhotosProvider);
    final photoCount = ref.watch(photoCountProvider);
    final canLoadMore = ref.watch(canLoadMorePhotosProvider);
    final hasLoadMoreError = ref.watch(hasLoadMoreErrorProvider);

    return Scaffold(
      appBar: PmAppBar(
        titleText: 'Latest Photos${photoCount > 0 ? ' ($photoCount)' : ''}',
        actions: [
          // Show checking indicator
          if (isCheckingNew)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Center(
                child: SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: isRefreshing
                ? null
                : () {
                    ref.read(latestPhotoIdsAsyncProvider.notifier).reload();
                  },
          ),
        ],
      ),
      body: RefreshIndicator(
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _onRefresh,
        child: Column(
          children: [
            // Show refresh progress
            if (isRefreshing) const LinearProgressIndicator(minHeight: 2),
            Expanded(
              child: photoListAsync.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stackTrace) => Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Failed to load photos',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          error.toString(),
                          style: Theme.of(context).textTheme.bodySmall,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () {
                            ref
                                .read(latestPhotoIdsAsyncProvider.notifier)
                                .reload();
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
                data: (photos) {
                  if (!hasPhotos) {
                    return const Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.photo_library_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No photos available',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: _scrollController,
                    itemCount:
                        photos.length +
                        (canLoadMore || hasLoadMoreError ? 1 : 0),
                    itemBuilder: (context, index) {
                      // Load more footer
                      if (index == photos.length) {
                        if (isLoadingMore) {
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        }

                        if (hasLoadMoreError) {
                          return Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Center(
                              child: Column(
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    color: Colors.orange,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Failed to load more photos',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                  if (loadMoreError != null) ...[
                                    const SizedBox(height: 4),
                                    Text(
                                      loadMoreError.toString(),
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodySmall,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                  const SizedBox(height: 8),
                                  TextButton.icon(
                                    onPressed: () {
                                      final notifier = ref.read(
                                        latestPhotoIdsAsyncProvider.notifier,
                                      );
                                      notifier.clearLoadMoreError();
                                      notifier.loadMore();
                                    },
                                    icon: const Icon(Icons.refresh),
                                    label: const Text('Retry'),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        // End of list indicator (optional)
                        return const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: Text(
                              'No more photos',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ),
                        );
                      }

                      final photo = photos[index];

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0,
                        ),
                        child: ListTile(
                          leading: ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: CachedNetworkImage(
                              imageUrl: photo.url,
                              placeholder: (context, url) => Container(
                                width: 60,
                                height: 60,
                                color: Colors.grey[200],
                                child: const Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: 60,
                                height: 60,
                                color: Colors.grey[300],
                                child: const Icon(
                                  Icons.broken_image,
                                  color: Colors.grey,
                                ),
                              ),
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                            ),
                          ),
                          title: Text(
                            photo.imageTitle,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Text('ID: ${photo.id}'),
                          trailing: const Icon(
                            Icons.arrow_forward_ios,
                            size: 16,
                          ),
                          onTap: () {
                            // Handle photo tap (navigate to detail screen, etc.)
                            // Navigator.push(context, MaterialPageRoute(...));
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
