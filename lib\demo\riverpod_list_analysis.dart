// latest_photo_list_screen.dart

import 'package:portraitmode/app/utils/log_util.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/widgets/blank_screen.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/demo/riverpod_ids_analysis_provider.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';

class RiverpodListAnalysis extends ConsumerStatefulWidget {
  const RiverpodListAnalysis({super.key});

  @override
  RiverpodListAnalysisState createState() => RiverpodListAnalysisState();
}

class RiverpodListAnalysisState extends ConsumerState<RiverpodListAnalysis> {
  final int _loadMorePerPage = 50;
  int _loadMoreLastId = 0;

  final PhotoListService _photoListService = PhotoListService();

  @override
  void initState() {
    _triggerLoadMore();

    pmLog('initState RiverpodReactivityAnalysis');
    super.initState();
  }

  @override
  void dispose() {
    pmLog('dispose RiverpodReactivityAnalysis');
    super.dispose();
  }

  /// Trigger load more.
  ///
  /// The `_canLoadMore` checking should be in the caller, not here.
  Future<void> _triggerLoadMore() async {
    await _handleLoadMore();
  }

  @override
  Widget build(BuildContext context) {
    pmLog('🛠️ Building RiverpodReactivityAnalysis screen');

    return Scaffold(
      appBar: PmAppBar(titleText: 'Riverpod Reactivity Analysis'),
      body: SafeArea(
        child: Builder(
          builder: (context) {
            final List<int> photoIdList = ref.watch(
              riverpodIdsAnalysisProvider,
            );

            return ListView.builder(
              itemCount: photoIdList.length,
              itemBuilder: (BuildContext context, int index) {
                final double marginTop = 12.0;

                pmLog('🛠️ Building item with id: "${photoIdList[index]}"');

                return Padding(
                  padding: EdgeInsets.only(top: marginTop),
                  child: ListTile(
                    key: ValueKey(photoIdList[index]),
                    title: Text('Photo "${photoIdList[index]}"'),
                    tileColor: Colors.blueAccent.withAlpha(25),
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => BlankScreen()),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Future<bool> _handleLoadMore() async {
    final photoListResponse = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
    );

    final isFirstLoad = _loadMoreLastId == 0;

    _handlePhotoListResponse(photoListResponse, false, isFirstLoad);

    return photoListResponse.success;
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    ref
        .read(riverpodIdsAnalysisProvider.notifier)
        .replaceAll(response.data.map((photo) => photo.id).toList());
  }
}
