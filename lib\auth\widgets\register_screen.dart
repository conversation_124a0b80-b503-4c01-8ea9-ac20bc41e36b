import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_place/google_place.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/device_util.dart';
import 'package:portraitmode/app/widgets/main_screen.dart';
import 'package:portraitmode/auth/dto/auth_data.dart';
import 'package:portraitmode/auth/http_responses/auth_response.dart';
import 'package:portraitmode/auth/services/auth_service.dart';
import 'package:portraitmode/auth/widgets/privacy_policy_link.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/hive/dto/local_auth_data.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/location_picker/location_picker.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  RegisterScreenState createState() => RegisterScreenState();
}

class RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _authService = AuthService();
  final GooglePlace _googlePlace = GooglePlace(GoogleConfig.mapApiKey);

  final ValueNotifier<String?> _errorNotifier = ValueNotifier(null);

  AuthResponse? authResponse;

  double? _locationLat;
  double? _locationLng;
  String _locationAddress = '';

  final _usernameFieldController = TextEditingController();
  final _emailFieldController = TextEditingController();
  final _passwordFieldController = TextEditingController();
  final _firstNameFieldController = TextEditingController();
  final _lastNameFieldController = TextEditingController();
  final _locationFieldController = TextEditingController();

  final double registerLinkHeight = 50.0;
  late final bool isIOS;

  @override
  void initState() {
    super.initState();
    isIOS = Platform.isIOS;
  }

  @override
  void dispose() {
    _usernameFieldController.dispose();
    _emailFieldController.dispose();
    _passwordFieldController.dispose();
    _firstNameFieldController.dispose();
    _lastNameFieldController.dispose();
    _locationFieldController.dispose();

    _errorNotifier.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.sizeOf(context).width;

    return Scaffold(
      backgroundColor: context.colors.lightColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Center(
            child: MaxWidthBuilder(
              maxWidth: 520.0,
              builder: (BuildContext context, BoxConstraints constraints) {
                double containerWidth = constraints.maxWidth;

                return Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Form(
                      key: _formKey,
                      child: SizedBox(
                        width: (containerWidth / 100) * 85,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 60.0,
                                bottom: 8.0,
                              ),
                              child: Image.asset(
                                context.isDarkMode
                                    ? "assets/logo-white.png"
                                    : "assets/logo.png",
                                width: screenWidth - (screenWidth / 3.5),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 50.0),
                              child: Text(
                                "The space your photos deserve.",
                                style: TextStyle(
                                  color: context.colors.primarySwatch[400],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 16.0,
                              ),
                              child: PmTextField(
                                controller: _usernameFieldController,
                                labelText: "Username*",
                                validator:
                                    FieldValidators.usernameValidator.call,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 16.0,
                              ),
                              child: PmTextField(
                                controller: _emailFieldController,
                                keyboardType: TextInputType.emailAddress,
                                labelText: "Email*",
                                validator: FieldValidators.emailValidator.call,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 16.0,
                              ),
                              child: PmTextField(
                                controller: _passwordFieldController,
                                labelText: "Password*",
                                obscureText: true,
                                validator:
                                    FieldValidators.passwordValidator.call,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 16.0,
                              ),
                              child: PmTextField(
                                controller: _firstNameFieldController,
                                labelText: "First Name*",
                                validator:
                                    FieldValidators.firstNameValidator.call,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 16.0,
                              ),
                              child: PmTextField(
                                controller: _lastNameFieldController,
                                labelText: "Last Name*",
                                validator:
                                    FieldValidators.lastNameValidator.call,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 40.0,
                              ),
                              child: PmTextField(
                                controller: _locationFieldController,
                                readOnly: true,
                                labelText: Platform.isIOS
                                    ? "Location"
                                    : "Location*",
                                validator: isIOS
                                    ? FieldValidators.iOSlocationValidator.call
                                    : FieldValidators.locationValidator.call,
                                onTap: () async {
                                  _openLocationPicker();
                                },
                              ),
                            ),
                            SizedBox(
                              height: 50.0,
                              width: double.infinity,
                              child: SubmitButton(
                                buttonText: "Register",
                                onPressed: onFormSubmit,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 16.0,
                                bottom: 16.0,
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    "Fields marked with * are required.",
                                    style: TextStyle(
                                      color: context.colors.primarySwatch[400],
                                      fontSize: 13.0,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const SizedBox(height: 3.0),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "Already have an account?",
                                        style: TextStyle(
                                          color:
                                              context.colors.primarySwatch[400],
                                          fontSize: 13.0,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      const SizedBox(width: 5.0),
                                      InkWell(
                                        onTap: onLoginLinkTap,
                                        child: Text(
                                          "Login",
                                          style: TextStyle(
                                            color: context.colors.accentColor,
                                            fontSize: 13.0,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            ValueListenableBuilder(
                              valueListenable: _errorNotifier,
                              builder: (context, errorMsg, _) {
                                return errorMsg != null
                                    ? Text(
                                        errorMsg,
                                        style: const TextStyle(
                                          color: Colors.red,
                                        ),
                                      )
                                    : const SizedBox.shrink();
                              },
                            ),
                            const SizedBox(height: 120.0),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: Platform.isIOS ? 40.0 : 20.0,
                      child: PrivacyPolicyLink(),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _openLocationPicker() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return LocationPicker(onChanged: _handleLocationChanged);
        },
      ),
    );
  }

  void _handleLocationChanged(AutocompletePrediction prediction) async {
    _locationFieldController.text = prediction.description ?? _locationAddress;
    _locationAddress = prediction.description ?? _locationAddress;

    DetailsResponse? response = await _getDetailedResult(prediction);
    if (response == null) return;

    DetailsResult? result = response.result;
    if (result == null) return;
    if (result.formattedAddress == null) return;

    _locationFieldController.text =
        result.formattedAddress ?? _locationFieldController.text;

    Geometry? geometry = result.geometry;
    if (geometry == null) return;

    Location? location = geometry.location;
    if (location == null) return;
    if (location.lat == null || location.lng == null) return;

    _locationAddress = result.formattedAddress ?? _locationAddress;
    _locationLat = location.lat;
    _locationLng = location.lng;
  }

  Future<DetailsResponse?> _getDetailedResult(
    AutocompletePrediction prediction,
  ) async {
    if (prediction.placeId == null) return null;

    DetailsResponse? result = await _googlePlace.details.get(
      prediction.placeId ?? '',
    );

    return result;
  }

  void onLoginLinkTap() {
    Navigator.pop(context);
  }

  Future<void> onFormSubmit() async {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) {
      return;
    }

    final String deviceModel = await getDeviceModelSlug();

    authResponse = await _authService.register(
      username: _usernameFieldController.text,
      email: _emailFieldController.text,
      password: _passwordFieldController.text,
      firstName: _firstNameFieldController.text,
      lastName: _lastNameFieldController.text,
      location: _locationFieldController.text,
      locationLat: _locationLat,
      locationLng: _locationLng,
      device: deviceModel,
    );

    if (!mounted) return;

    if (authResponse!.success) {
      AuthData data = authResponse!.data ?? AuthData();

      await LocalUserService.replace(
        LocalUserData(
          userId: data.id,
          nicename: data.nicename,
          role: data.role,
          displayName: data.displayName,
          profileUrl: data.profileUrl,
          avatarUrl: data.avatarUrl,
          membershipType: data.membershipType,
        ),
      );

      // pmLog('The accessToken after registration is: ${data.accessToken}');
      // pmLog('The refreshToken after registration is: ${data.refreshToken}');

      await LocalAuthService.replace(
        LocalAuthData(
          isLoggedIn: true,
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
        ),
      );

      ProfileData profileData = ProfileData(
        id: data.id,
        nicename: data.nicename,
        role: data.role,
        email: data.email,
        website: data.website,
        instagram: data.instagram,
        profileUrl: data.profileUrl,
        avatarUrl: data.avatarUrl,
        firstName: data.firstName,
        lastName: data.lastName,
        displayName: data.displayName,
        description: data.description,
        location: data.location,
        latestPhotoUrl: data.latestPhotoUrl,
        totalPhotos: data.totalPhotos,
        camera: data.camera,
        focalLength: data.focalLength,
        isFollowing: data.isFollowing,
        totalFollowing: data.totalFollowing,
        totalFollowers: data.totalFollowers,
        membershipType: data.membershipType,
      );

      ref.read(profileProvider.notifier).replace(profileData);

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }

      return;
    }

    _errorNotifier.value = authResponse!.message;
  }
}
