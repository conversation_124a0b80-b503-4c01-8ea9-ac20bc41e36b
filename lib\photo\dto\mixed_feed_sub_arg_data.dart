class MixedFeedSubArgData {
  final int limit;
  final int? lastId;

  MixedFeedSubArgData({this.limit = 8, this.lastId});

  MixedFeedSubArgData copyWith({int? limit, int? lastId}) {
    return MixedFeedSubArgData(
      limit: limit ?? this.limit,
      lastId: lastId ?? this.lastId,
    );
  }

  Map<String, dynamic> toMap() {
    return {'limit': limit, 'lastId': lastId};
  }

  factory MixedFeedSubArgData.fromMap(Map<String, dynamic> map) {
    return MixedFeedSubArgData(limit: map['limit'] ?? 8, lastId: map['lastId']);
  }
}
