import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/http_responses/follow_response.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/following_artists_provider.dart';
import 'package:portraitmode/artist/services/follow_artist_service.dart';

class FollowButton extends ConsumerStatefulWidget {
  final int artistId;
  final bool isFollowing;
  final Function(FollowActionType action, FollowResponseData? data)?
  onFollowStatusChanged;

  const FollowButton({
    super.key,
    required this.artistId,
    this.isFollowing = false,
    this.onFollowStatusChanged,
  });

  @override
  FollowButtonState createState() => FollowButtonState();
}

class FollowButtonState extends ConsumerState<FollowButton> {
  final followArtistService = FollowArtistService();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // pmLog('Building follow icon button of photo id: ${widget.artistId}');

    final bool isFollowing = widget.isFollowing;

    return SizedBox(
      height: 35.0,
      child: (isFollowing ? followedButton() : followButton()),
    );
  }

  Widget followButton() {
    return ElevatedButton(
      onPressed: () {
        doAction(FollowActionType.follow);
      },
      style: ElevatedButton.styleFrom(
        elevation: 0.0,
        backgroundColor: context.isDarkMode
            ? AppColorsCache.dark().baseColorAlt
            : AppColorsCache.light().brandColor,
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(35.0),
        ),
      ),
      child: const Text(
        'Follow',
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 12.0,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget followedButton() {
    return ElevatedButton(
      onPressed: () async {
        doAction(FollowActionType.unfollow);
      },
      style: ElevatedButton.styleFrom(
        elevation: 0.0,
        backgroundColor: context.colors.accentColor,
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(35.0),
        ),
      ),
      child: const Text(
        'Following',
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 12.0,
          color: Colors.white,
        ),
      ),
    );
  }

  Future<void> doAction(FollowActionType action) async {
    // Switch the isFollowing state even though the request hasn't been made.
    widget.onFollowStatusChanged?.call(action, null);

    FollowResponse response = action == FollowActionType.follow
        ? await followArtistService.follow(widget.artistId)
        : await followArtistService.unfollow(widget.artistId);

    if (!response.success || response.data == null) {
      // Switch the following state back to the previous state.
      widget.onFollowStatusChanged?.call(
        action == FollowActionType.follow
            ? FollowActionType.unfollow
            : FollowActionType.follow,
        null,
      );

      return;
    }

    if (action == FollowActionType.unfollow) {
      ref.read(followingArtistIdsProvider.notifier).removeItem(widget.artistId);
    } else {
      ref.read(followingArtistIdsProvider.notifier).addItem(widget.artistId);
    }

    widget.onFollowStatusChanged?.call(action, response.data);
  }
}
