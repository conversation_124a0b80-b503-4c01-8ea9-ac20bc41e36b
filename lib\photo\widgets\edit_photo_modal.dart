import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/widgets/pm_chip.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/providers/categories_provider.dart';
import 'package:portraitmode/category/widgets/categories_picker.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/dialogs/success_dialog.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle_delegate.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/upload_photo_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/edit_photo_service.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';

class EditPhotoModal extends ConsumerStatefulWidget {
  const EditPhotoModal({super.key, required this.photo});

  final PhotoData photo;

  @override
  EditPhotoModalState createState() => EditPhotoModalState();
}

class EditPhotoModalState extends ConsumerState<EditPhotoModal> {
  final _formKey = GlobalKey<FormState>();

  final _locationFieldController = TextEditingController();
  final _descriptionFieldController = TextEditingController();
  late final ValueNotifier<List<int>> _selectedCategoryIdsNotifier;

  @override
  void initState() {
    super.initState();

    _selectedCategoryIdsNotifier = ValueNotifier(widget.photo.categories);
    _descriptionFieldController.text = widget.photo.description;
  }

  @override
  void dispose() {
    _locationFieldController.dispose();
    _descriptionFieldController.dispose();
    _selectedCategoryIdsNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    pmLog('✏️ Building EditPhotoModal');

    return DraggableScrollableSheet(
      maxChildSize: BottomSheetConfig.maxChildSize,
      initialChildSize: 0.45,
      expand: false,
      builder: (context, scrollController) {
        return SafeArea(
          child: Form(
            key: _formKey,
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.viewInsetsOf(context).bottom,
              ),
              child: CustomScrollView(
                controller: scrollController,
                slivers: [
                  SliverPersistentHeader(
                    pinned: true,
                    floating: false,
                    delegate: ModalDragHandleDelegate(),
                  ),
                  SliverList(
                    delegate: SliverChildListDelegate([
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: 16.0,
                          left: ScreenStyleConfig.horizontalPadding,
                          right: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: PmTextField(
                          controller: _descriptionFieldController,
                          labelText: "Caption",
                          minLines: 2,
                          maxLines: 7,
                          // validator: FieldValidators.photoDescriptionValidator,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 8.0,
                          bottom: 16.0,
                          left: ScreenStyleConfig.horizontalPadding,
                          right: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.0),
                            border: Border.all(
                              color: context.colors.borderColor,
                              width: 1.0,
                            ),
                          ),
                          child: ValueListenableBuilder(
                            valueListenable: _selectedCategoryIdsNotifier,
                            builder: (context, selectedCategoryIds, child) {
                              final selectedCategories = ref.watch(
                                severalCategoriesProvider(selectedCategoryIds),
                              );

                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              CategoriesPicker(
                                                selectedCategories:
                                                    selectedCategories,
                                                onClose: (cats) {
                                                  FocusScope.of(
                                                    context,
                                                  ).unfocus();

                                                  _selectedCategoryIdsNotifier
                                                      .value = cats
                                                      .map((cat) => cat.id)
                                                      .toList();
                                                },
                                              ),
                                        ),
                                      );
                                    },
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Ionicons.add_circle,
                                          color: context.colors.accentColor,
                                          size: 20.0,
                                        ),
                                        const SizedBox(width: 4.0),
                                        Text(
                                          "Add categories",
                                          style: TextStyle(
                                            color: context.colors.accentColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (selectedCategoryIds.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        left: 4.0,
                                        right: 4.0,
                                        bottom: 8.0,
                                      ),
                                      child: Wrap(
                                        spacing: 8.0,
                                        runSpacing: 8.0,
                                        children: selectedCategories
                                            .map(
                                              (cat) => PmChip(
                                                label: Text(cat.name),
                                                onDeleted: () {
                                                  _selectedCategoryIdsNotifier
                                                          .value =
                                                      _selectedCategoryIdsNotifier
                                                          .value
                                                          .where(
                                                            (id) =>
                                                                id != cat.id,
                                                          )
                                                          .toList();
                                                },
                                              ),
                                            )
                                            .toList(),
                                      ),
                                    ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 8.0,
                          bottom: 16.0,
                          left: ScreenStyleConfig.horizontalPadding,
                          right: ScreenStyleConfig.horizontalPadding,
                        ),
                        child: ValueListenableBuilder(
                          valueListenable: _selectedCategoryIdsNotifier,
                          builder: (context, selectedCategoryIds, child) {
                            return SubmitButton(
                              buttonText: "Update",
                              width: double.infinity,
                              height: 40.0,
                              fontWeight: FontWeight.w600,
                              onPressed: () =>
                                  _handleSubmission(selectedCategoryIds),
                            );
                          },
                        ),
                      ),
                    ]),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  final _editPhotoService = EditPhotoService();

  Future<void> _handleSubmission(List<int> categoryIds) async {
    if (!_formKey.currentState!.validate()) return;

    final UploadPhotoResponse response = await _editPhotoService.submit(
      photoId: widget.photo.id,
      description: _descriptionFieldController.text,
      categoryIds: categoryIds,
    );

    if (!response.success || response.data == null) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    PhotoData data = response.data!;

    // Update the matched photo in the global myAlbumPhotoListProviderMap.
    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      AutoDisposeNotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
      provider,
    ) {
      List<PhotoData> photos = ref.read(provider);
      int matchedIndex = photos.indexWhere((photo) => photo.id == data.id);

      if (matchedIndex != -1) {
        ref.read(provider.notifier).updateItem(data);
      }
    });

    ref
        .read(photoStoreProvider.notifier)
        .updateItem(data, addIfNotExists: true);

    if (mounted) {
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );

      showSuccessDialog(context, message: response.message);
    }
  }
}
