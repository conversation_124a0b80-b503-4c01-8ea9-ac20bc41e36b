import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/artist/widgets/membership_badge.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';

class PhotoAuthor extends StatelessWidget {
  final int authorId;
  final String authorNicename;
  final String authorDisplayName;
  final String authorProfileUrl;
  final String authorAvatarUrl;
  final String authorMembershipType;
  final EdgeInsets? padding;
  final bool hasDivider;
  final double contentToDividerGap;

  const PhotoAuthor({
    super.key,
    required this.authorId,
    required this.authorNicename,
    required this.authorDisplayName,
    required this.authorProfileUrl,
    required this.authorAvatarUrl,
    required this.authorMembershipType,
    this.padding,
    this.hasDivider = true,
    this.contentToDividerGap = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    const double rowGap = 13.0;

    return Padding(
      padding: padding != null ? padding! : const EdgeInsets.all(0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Avatar(
                imageUrl: authorAvatarUrl,
                size: 34.0,
                useBorder: true,
                onTap: () {
                  _gotoArtistDetailScreen(context);
                },
              ),
              const SizedBox(width: 7.0),
              Flexible(
                child: InkWell(
                  onTap: () {
                    _gotoArtistDetailScreen(context);
                  },
                  child: Row(
                    children: [
                      Text(
                        authorDisplayName,
                        style: const TextStyle(fontSize: 13.0),
                      ),
                      if (authorMembershipType.isNotEmpty)
                        MembershipBadge(membershipType: authorMembershipType),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: rowGap),
          if (hasDivider) SizedBox(height: contentToDividerGap),
          if (hasDivider)
            Divider(height: 1.0, color: context.colors.borderColor),
        ],
      ),
    );
  }

  void _gotoArtistDetailScreen(BuildContext context) {
    final bool isOwnProfile = LocalUserService.userId == authorId;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          isOwnProfile: isOwnProfile,
          partialData: ArtistPartialData(
            id: authorId,
            nicename: authorNicename,
            displayName: authorDisplayName,
            profileUrl: authorProfileUrl,
            avatarUrl: authorAvatarUrl,
            membershipType: authorMembershipType,
          ),
        ),
      ),
    );
  }
}
