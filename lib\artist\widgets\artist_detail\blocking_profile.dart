import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/bottom_sheets/other_profile_bottom_sheet.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';

class BlockingProfile extends StatefulWidget {
  const BlockingProfile({
    super.key,
    this.useBackButton = true,
    required this.containerWidth,
    required this.artist,
  });

  final bool useBackButton;
  final double containerWidth;
  final ArtistData artist;

  @override
  BlockingProfileState createState() => BlockingProfileState();
}

class BlockingProfileState extends State<BlockingProfile> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: <Widget>[
            PmSliverAppBar(
              scrollController: _scrollController,
              automaticallyImplyLeading: widget.useBackButton,
              backgroundColor: context.colors.lightColor,
              titleText: widget.useBackButton
                  ? '@${widget.artist.nicename}'
                  : '',
              useLogo: widget.useBackButton ? false : true,
              actions: [
                ProfileMenuIndicator(
                  onTap: () {
                    _showOtherProfileBottomSheet();
                  },
                ),
              ],
            ),
            SliverToBoxAdapter(
              child: Container(
                color: context.colors.lightColor,
                padding: const EdgeInsets.only(
                  top: LayoutConfig.contentTopGap,
                  right: ScreenStyleConfig.horizontalPadding,
                  bottom: ScreenStyleConfig.verticalPadding + 20.0,
                  left: ScreenStyleConfig.horizontalPadding,
                ),
                child: ProfileHeader(
                  artist: widget.artist,
                  showPhotoMeta: false,
                  showLocation: false,
                  showWebsite: false,
                  showFollowers: false,
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: ScreenStyleConfig.horizontalPadding + 30.0,
                  vertical: ScreenStyleConfig.verticalPadding + 20.0,
                ),
                child: Text(
                  "You are blocked from following @${widget.artist.nicename} and viewing @${widget.artist.nicename}'s photos.",
                  style: TextStyle(
                    fontSize: 16.0,
                    color: context.isDarkMode
                        ? AppColorsCache.dark().greyColor
                        : AppColorsCache.light().brandColorAlt,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOtherProfileBottomSheet() {
    final ArtistData artist = widget.artist;

    showModalBottomSheet(
      context: context,
      isDismissible: true,
      useSafeArea: true,
      builder: (BuildContext context) {
        return OtherProfileBottomSheet(
          artist: artist,
          showBlockUnblockMenu: false,
        );
      },
    );
  }
}
