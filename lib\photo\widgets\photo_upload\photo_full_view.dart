import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:portraitmode/app/config/colors.dart';

class PhotoFullView extends StatelessWidget {
  const PhotoFullView({super.key, required this.photoFile});

  final File photoFile;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorsCache.light().brandColor,
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          alignment: Alignment.center,
          child: _buildPhoto(),
        ),
      ),
    );
  }

  Widget _buildPhoto() {
    return PhotoView(
      imageProvider: FileImage(File(photoFile.path)),
      minScale: PhotoViewComputedScale.contained,
    );
  }
}
