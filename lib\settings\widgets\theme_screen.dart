import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/utils/theme_manager.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/settings/widgets/theme_screen/theme_screen_list_tile.dart';

class ThemeScreen extends StatefulWidget {
  const ThemeScreen({super.key});

  @override
  ThemeScreenState createState() => ThemeScreenState();
}

class ThemeScreenState extends State<ThemeScreen> {
  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        scrollController: _scrollController,
        titleText: 'Theme',
        automaticallyImplyLeading: true,
        useLogo: false,
        actions: const [],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Padding(
            padding: const EdgeInsets.only(left: 3.0),
            child: ValueListenableBuilder<ThemeMode>(
              valueListenable: ThemeManager.instance,
              builder: (context, themeMode, child) {
                return RadioGroup<String>(
                  groupValue: themeMode.name,
                  onChanged: _handleThemeChange,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: LayoutConfig.contentTopGap),
                      ThemeScreenListTile(
                        title: 'Light mode',
                        value: 'light',
                        selected: themeMode == ThemeMode.light,
                      ),
                      const SizedBox(height: LayoutConfig.contentTopGap),
                      ThemeScreenListTile(
                        title: 'Dark mode',
                        value: 'dark',
                        selected: themeMode == ThemeMode.dark,
                      ),
                      const SizedBox(height: LayoutConfig.contentTopGap),
                      ThemeScreenListTile(
                        title: 'System default',
                        value: 'system',
                        selected: themeMode == ThemeMode.system,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _handleThemeChange(String? value) {
    pmLog('Switching theme mode to: $value');

    switch (value) {
      case 'light':
        ThemeManager.instance.setLightTheme();
        break;
      case 'dark':
        ThemeManager.instance.setDarkTheme();
        break;
      case 'system':
        ThemeManager.instance.setSystemTheme();
        break;
    }
  }
}
