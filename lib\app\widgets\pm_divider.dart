import 'package:flutter/widgets.dart';
import 'package:portraitmode/app/config/colors.dart';

class PmDivider extends StatelessWidget {
  final double height;
  final EdgeInsetsGeometry? margin;

  const PmDivider({super.key, this.height = 1.0, this.margin});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      margin: margin,
      decoration: BoxDecoration(color: context.colors.borderColor),
    );
  }
}
