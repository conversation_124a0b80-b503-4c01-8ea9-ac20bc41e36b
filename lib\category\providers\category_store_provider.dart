// category_store_provider.dart

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/category/dto/category_data.dart';

/// A highly optimized and memory-efficient notifier for managing categories by ID.
///
/// Uses `Map<int, CategoryData>` for O(1) operations and eliminates the need for
/// index caching and list copying that plagued the previous implementation.
final class CategoryStoreNotifier
    extends AutoDisposeNotifier<Map<int, CategoryData>> {
  @override
  Map<int, CategoryData> build() {
    // Keep this provider alive because:
    // - The `ref.watch` calls are scattered across various child widgets.
    // - However, the actual assignment to the store happens higher up in the widget tree.
    //
    // Without keepAlive(), the provider might get disposed too early,
    // causing unexpected behavior or null data in child widgets.
    ref.keepAlive();

    return <int, CategoryData>{};
  }

  /// Creates a new state map to trigger provider updates.
  /// Only creates new map if there are actual changes.
  void _updateState(
    Map<int, CategoryData> Function(Map<int, CategoryData>) updater,
  ) {
    final newState = updater(Map<int, CategoryData>.from(state));
    if (!mapEquals(state, newState)) {
      state = newState;
    }
  }

  /// Gets a category by ID. Returns null if not found.
  CategoryData? getItem(int id) {
    return state[id];
  }

  /// Gets multiple categories by their IDs.
  /// Returns only the categories that exist, maintaining the order of requested IDs.
  List<CategoryData> getItems(List<int> ids) {
    if (ids.isEmpty) return const [];

    final result = <CategoryData>[];

    for (final id in ids) {
      final category = state[id];

      if (category != null) {
        result.add(category);
      }
    }

    return result;
  }

  /// Checks if a category with the given ID exists.
  bool hasItem(int id) {
    return state.containsKey(id);
  }

  /// Adds a new category only if it doesn't already exist.
  ///
  /// If [updateIfExists] is true, will update the existing category.
  void addItem(CategoryData newItem, {bool updateIfExists = true}) {
    if (state.containsKey(newItem.id)) {
      if (updateIfExists) updateItem(newItem);
      return;
    }

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Adds multiple categories efficiently.
  ///
  /// If [updateIfExists] is true, will update existing categories.
  void addItems(List<CategoryData> newItems, {bool updateIfExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          if (updateIfExists) {
            map[item.id] = item;
          }
        } else {
          map[item.id] = item;
        }
      }
      return map;
    });
  }

  /// Updates a single existing category.
  ///
  /// If [addIfNotExists] is true, will add the category if it doesn't exist.
  void updateItem(CategoryData newItem, {bool addIfNotExists = true}) {
    if (!state.containsKey(newItem.id)) {
      if (addIfNotExists) {
        addItem(newItem, updateIfExists: false);
      }
      return;
    }

    // Only update if the category has actually changed
    if (state[newItem.id] == newItem) return;

    _updateState((map) {
      map[newItem.id] = newItem;
      return map;
    });
  }

  /// Updates multiple categories efficiently.
  ///
  /// If [addIfNotExists] is true, will add categories that don't exist.
  void updateItems(List<CategoryData> newItems, {bool addIfNotExists = true}) {
    if (newItems.isEmpty) return;

    _updateState((map) {
      for (final item in newItems) {
        if (map.containsKey(item.id)) {
          map[item.id] = item;
        } else if (addIfNotExists) {
          map[item.id] = item;
        }
      }
      return map;
    });
  }

  /// Removes a category by ID.
  void removeItem(int id) {
    if (!state.containsKey(id)) return;

    _updateState((map) {
      map.remove(id);
      return map;
    });
  }

  /// Removes multiple categories by their IDs.
  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;

    // Filter to only IDs that actually exist
    final existingIds = ids.where((id) => state.containsKey(id)).toList();
    if (existingIds.isEmpty) return;

    _updateState((map) {
      for (final id in existingIds) {
        map.remove(id);
      }
      return map;
    });
  }

  /// Removes a category by slug.
  void removeBySlug(String slug) {
    final categoryToRemove = state.values
        .where((category) => category.slug == slug)
        .map((category) => category.id)
        .toList();

    if (categoryToRemove.isEmpty) return;

    _updateState((map) {
      for (final categoryId in categoryToRemove) {
        map.remove(categoryId);
      }
      return map;
    });
  }

  /// Replaces the entire store with a new collection of categories.
  void replaceAll(List<CategoryData> newList) {
    final newMap = <int, CategoryData>{};
    for (final category in newList) {
      newMap[category.id] = category;
    }
    state = newMap;
  }

  /// Clears all categories in the store.
  void clear() {
    if (state.isNotEmpty) {
      state = <int, CategoryData>{};
    }
  }

  // ------------------------------------------------------------
  // Specialized update methods for single category fields
  // These are now much more efficient with direct map access
  // ------------------------------------------------------------

  /// Updates a specific field of a category if it exists.
  void _updateCategoryField(
    int categoryId,
    CategoryData Function(CategoryData) updater,
  ) {
    final currentCategory = state[categoryId];
    if (currentCategory == null) return;

    final updatedCategory = updater(currentCategory);
    if (currentCategory != updatedCategory) {
      _updateState((map) {
        map[categoryId] = updatedCategory;
        return map;
      });
    }
  }

  void setPhotosCount(int categoryId, int newPhotosCount) {
    _updateCategoryField(
      categoryId,
      (category) => category.copyWith(photosCount: newPhotosCount),
    );
  }

  void setLatestPhotoUrl(int categoryId, String newLatestPhotoUrl) {
    _updateCategoryField(
      categoryId,
      (category) => category.copyWith(latestPhotoUrl: newLatestPhotoUrl),
    );
  }

  void incrementPhotosCount(int categoryId) {
    _updateCategoryField(
      categoryId,
      (category) => category.copyWith(photosCount: category.photosCount + 1),
    );
  }

  void decrementPhotosCount(int categoryId) {
    _updateCategoryField(
      categoryId,
      (category) => category.copyWith(
        photosCount: (category.photosCount - 1)
            .clamp(0, double.infinity)
            .toInt(),
      ),
    );
  }

  // ------------------------------------------------------------
  // Utility getters and methods
  // ------------------------------------------------------------

  /// Returns total number of categories
  int get count => state.length;

  /// Checks if the store is empty
  bool get isEmpty => state.isEmpty;

  /// Checks if the store has at least one category
  bool get isNotEmpty => state.isNotEmpty;

  /// Returns all category IDs
  List<int> get allIds => state.keys.toList();

  /// Returns all categories as a list
  List<CategoryData> get allCategories => state.values.toList();

  /// Gets a category by slug. Returns null if not found.
  CategoryData? getCategoryBySlug(String slug) {
    return state.values.where((category) => category.slug == slug).firstOrNull;
  }

  /// Gets multiple categories by their slugs.
  List<CategoryData> getCategoriesBySlugs(List<String> slugs) {
    if (slugs.isEmpty) return const [];

    final slugSet = slugs.toSet();
    return state.values
        .where((category) => slugSet.contains(category.slug))
        .toList();
  }
}

/// NotifierProvider for the main category store
final categoryStoreProvider =
    NotifierProvider.autoDispose<CategoryStoreNotifier, Map<int, CategoryData>>(
      CategoryStoreNotifier.new,
    );

/// A selective provider that only rebuilds when a specific category changes
final categoryProvider = Provider.family<CategoryData?, int>((ref, categoryId) {
  return ref.watch(categoryStoreProvider)[categoryId];
});

/// A selective provider for specific category fields to minimize rebuilds
final categoryPropsProvider =
    Provider.family<
      ({
        String? name,
        String? slug,
        String? url,
        String? latestPhotoUrl,
        int? photosCount,
      }),
      int
    >((ref, categoryId) {
      final category = ref.watch(categoryStoreProvider)[categoryId];
      return (
        name: category?.name,
        slug: category?.slug,
        url: category?.url,
        latestPhotoUrl: category?.latestPhotoUrl,
        photosCount: category?.photosCount,
      );
    });

/// Provider that returns the current total category count
final categoryCountProvider = Provider.autoDispose<int>((ref) {
  return ref.watch(
    categoryStoreProvider.select((categories) => categories.length),
  );
});

/// Provider that checks if the store has any categories
final hasCategoriesProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(
    categoryStoreProvider.select((categories) => categories.isNotEmpty),
  );
});

/// Provider that returns all categories as a list (for UI components that need lists)
final categoryListProvider = Provider.autoDispose<List<CategoryData>>((ref) {
  return ref.watch(
    categoryStoreProvider.select((categories) => categories.values.toList()),
  );
});

/// Provider that checks if a specific category ID exists
final hasCategoryProvider = Provider.family.autoDispose<bool, int>((
  ref,
  categoryId,
) {
  return ref.watch(
    categoryStoreProvider.select(
      (categories) => categories.containsKey(categoryId),
    ),
  );
});

/// Provider that returns a specific category by ID
final specificCategoryProvider = Provider.family
    .autoDispose<CategoryData?, int>((ref, categoryId) {
      return ref.watch(
        categoryStoreProvider.select((categories) => categories[categoryId]),
      );
    });

/// Provider that returns a category by slug
final categoryBySlugProvider = Provider.family
    .autoDispose<CategoryData?, String>((ref, slug) {
      return ref.watch(
        categoryStoreProvider.select(
          (categories) => categories.values
              .where((category) => category.slug == slug)
              .firstOrNull,
        ),
      );
    });
