import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/http_responses/profile_http_response.dart';

class ProfileService extends BaseService {
  Future<ProfileHttpResponse> edit({
    required ProfileData profileData,
    double? locationLat,
    double? locationLng,
  }) async {
    String apiUrl = "${URL.baseApiUrl}/my-profile/edit";

    try {
      final response = await http.post(
        apiUrl,
        data: {
          'email': profileData.email,
          'instagram': profileData.instagram,
          'first_name': profileData.firstName,
          'last_name': profileData.lastName,
          'location': profileData.location,
          'location_lat': locationLat != null ? locationLat.toString() : '',
          'location_lng': locationLng != null ? locationLng.toString() : '',
          'website': profileData.website,
          'camera': profileData.camera,
          'focal_length': profileData.focalLength,
          'description': profileData.description,
        },
      );

      return ProfileHttpResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ProfileHttpResponse.fromMap(e.response?.data);
      }

      return ProfileHttpResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ProfileHttpResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<ProfileHttpResponse> uploadAvatarFromBytes({
    required Uint8List bytes,
    String filename = 'avatar.jpg',
  }) async {
    String apiUrl = "${URL.baseApiUrl}/my-profile/avatar";

    try {
      FormData formData = FormData.fromMap({
        'avatar': MultipartFile.fromBytes(bytes, filename: filename),
      });

      final response = await http.post(
        apiUrl,
        data: formData,
        onSendProgress: (count, total) {
          // pmLog("Uploading avatar: $count/$total");
        },
      );

      return ProfileHttpResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ProfileHttpResponse.fromMap(e.response?.data);
      }

      return ProfileHttpResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ProfileHttpResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<ProfileHttpResponse> deleteAvatar() async {
    String apiUrl = "${URL.baseApiUrl}/my-profile/avatar";

    try {
      final response = await http.delete(apiUrl);

      return ProfileHttpResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ProfileHttpResponse.fromMap(e.response?.data);
      }

      return ProfileHttpResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ProfileHttpResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> likedPhotos({
    int limit = 15,
    int lastId = 0,
  }) async {
    String url =
        '${URL.baseApiUrl}/my-profile/liked-photos/${limit.toString()}/${lastId.toString()}';

    try {
      final response = await http.get(url);

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> archivedPhotos({
    int limit = 15,
    int lastId = 0,
  }) async {
    String url =
        '${URL.baseApiUrl}/my-profile/archived-photos/${limit.toString()}/${lastId.toString()}';

    try {
      final response = await http.get(url);

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}
