import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

@immutable
final class AlbumData {
  const AlbumData({
    this.slug = '',
    this.text = '',
    this.totalPhotos = 0,
    this.photos = const [],
  });

  final String slug;
  final String text;
  final int totalPhotos;
  final List<PhotoData> photos;

  AlbumData copyWith({
    String? slug,
    String? text,
    int? totalPhotos,
    List<PhotoData>? photos,
  }) {
    return AlbumData(
      slug: slug ?? this.slug,
      text: text ?? this.text,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      photos: photos ?? this.photos,
    );
  }

  factory AlbumData.fromMap(Map<String, dynamic> data) {
    return AlbumData(
      slug: data['slug'],
      text: data['text'],
      totalPhotos: data['totalPhotos'],
      photos: data['photos'] != null && data['photos'] is List
          ? (data['photos'] as List).map((e) => PhotoData.fromMap(e)).toList()
          : [],
    );
  }

  factory AlbumData.fromJson(String source) =>
      AlbumData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(slug, text, totalPhotos, photos);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AlbumData) return false;

    return other.slug == slug &&
        other.text == text &&
        other.totalPhotos == totalPhotos &&
        listEquals(other.photos, photos);
  }

  @override
  String toString() {
    return '''
AlbumData(
  slug: $slug,
  text: $text,
  totalPhotos: $totalPhotos,
  photos: $photos,
)
''';
  }
}
