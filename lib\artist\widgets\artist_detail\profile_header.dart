import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/utils/artist_util.dart';
import 'package:portraitmode/artist/widgets/artist_detail/edit_profile_button.dart';
import 'package:portraitmode/artist/widgets/artist_detail/followers_number.dart';
import 'package:portraitmode/artist/widgets/artist_detail/following_number.dart';
import 'package:portraitmode/artist/widgets/artist_detail/website_info.dart';
import 'package:portraitmode/artist/widgets/blocked_button.dart';
import 'package:portraitmode/artist/widgets/follow_button.dart';
import 'package:portraitmode/artist/widgets/membership_badge.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';
import 'package:portraitmode/profile/widgets/edit_profile_screen.dart';

class ProfileHeader extends ConsumerWidget {
  const ProfileHeader({
    super.key,
    required this.artist,
    this.showPhotoImage = true,
    this.showPhotoMeta = true,
    this.showLocation = true,
    this.useLocationIcon = false,
    this.showWebsite = true,
    this.useLinkIcon = false,
    this.showFollowers = true,
  });

  final ArtistData artist;
  final bool showPhotoImage;
  final bool showPhotoMeta;
  final bool showLocation;
  final bool useLocationIcon;
  final bool showWebsite;
  final bool useLinkIcon;
  final bool showFollowers;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final int profileId = LocalUserService.userId ?? 0;
    final bool isOwnProfile = artist.id == profileId;

    final String avatarUrl = isOwnProfile
        ? ref.watch(profileProvider.select((props) => props.avatarUrl))
        : artist.avatarUrl;

    final artistField = ref.watch(artistPropsProvider(artist.id));
    final bool isBlocked = artistField.isBlocked == true;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Avatar(
            imageUrl: avatarUrl,
            size: 80.0,
            useBorder: true,
            borderColor: Colors.white,
            isOwnAvatar: isOwnProfile,
            onTap: () {
              if (!isOwnProfile) {
                return;
              }

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EditProfileScreen(),
                ),
              );
            },
          ),
          const SizedBox(height: 8.0),
          Text(
            '@${artist.nicename}',
            style: TextStyle(
              fontSize: 12.0,
              color: context.colors.primarySwatch[200],
            ),
          ),
          const SizedBox(height: 5.0),
          Row(
            children: [
              Text(
                artist.displayName,
                style: TextStyle(
                  // Fill the "color" based on textTheme's display color.
                  color: Theme.of(context).textTheme.bodySmall!.color,
                  fontSize: 18.0,
                  fontWeight: FontWeight.w700,
                ),
              ),
              if (artist.membershipType.isNotEmpty)
                MembershipBadge(
                  membershipType: artist.membershipType,
                  size: 13.0,
                ),
            ],
          ),
          (artist.description.isNotEmpty
              ? const SizedBox(height: 15.0)
              : const SizedBox.shrink()),
          (artist.description.isNotEmpty
              ? Text(
                  artist.description,
                  style: TextStyle(
                    height: 1.4,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                )
              : const SizedBox.shrink()),
          if (showLocation) const SizedBox(height: 19.0),
          if (isBlocked) const SizedBox(height: 15.0),
          if (isBlocked) BlockedButton(artist: artist),
          if (showLocation)
            Row(
              children: [
                if (useLocationIcon)
                  Icon(
                    Icons.location_on_outlined,
                    size: 17.0,
                    color: context.colors.primarySwatch[200],
                  ),
                if (useLocationIcon) const SizedBox(width: 4.0),
                Expanded(
                  child: SizedBox(
                    child: Text(
                      artist.location,
                      style: TextStyle(
                        color: context.colors.primarySwatch[200],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 3,
                    ),
                  ),
                ),
              ],
            ),
          if (showWebsite) const SizedBox(height: 4.0),
          if (showWebsite)
            WebsiteInfo(
              website: artist.website.isNotEmpty
                  ? artist.website
                  : 'https://portraitmode.io/profile/${artist.nicename}',
            ),
          if (showFollowers) const SizedBox(height: 19.0),
          if (showFollowers)
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                FollowersNumber(
                  artistId: artist.id,
                  totalFollowers: artist.totalFollowers,
                  textColor: context.colors.primarySwatch[200],
                ),
                const SizedBox(width: 25.0),
                FollowingNumber(
                  artistId: artist.id,
                  totalFollowing: artist.totalFollowing,
                  textColor: context.colors.primarySwatch[200],
                ),
                const SizedBox(width: 25.0),
                (isOwnProfile
                    ? EditProfileButton(artistId: artist.id)
                    : FollowButton(
                        artistId: artist.id,
                        isFollowing: artist.isFollowing,
                        onFollowStatusChanged: (action, data) =>
                            handleFollowStatusChanged(
                              ref: ref,
                              action: action,
                              artist: artist,
                              data: data,
                            ),
                      )),
              ],
            ),
        ],
      ),
    );
  }
}
