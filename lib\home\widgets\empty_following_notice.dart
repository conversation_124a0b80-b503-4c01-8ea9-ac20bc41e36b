import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/search/widgets/search_screen_active.dart';

class EmptyFollowingNotice extends StatelessWidget {
  const EmptyFollowingNotice({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(right: 40.0, left: 40.0, bottom: 20.0),
      alignment: Alignment.center,
      // Add border radius and 1.0 border.
      color: context.colors.scaffoldColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          InkWell(
            enableFeedback: true,
            onTap: () => _gotoArtistSearchScreen(context),
            child: Icon(
              Ionicons.person_add_outline,
              size: 56.0,
              color: context.colors.brandColor,
            ),
          ),
          const SizedBox(height: 20.0),
          Text(
            'Follow artists',
            style: TextStyle(
              fontSize: 32.0,
              fontWeight: FontWeight.w600,
              color: context.colors.brandColor,
            ),
          ),
          const SizedBox(height: 16.0),

          /// The full text will be "See recently uploaded artists and explore other people's profiles."
          /// But we want to highlight the "See recently uploaded artists" part and make it tap-able.
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                WidgetSpan(
                  alignment: PlaceholderAlignment.baseline,
                  baseline: TextBaseline.alphabetic,
                  child: InkWell(
                    onTap: () {
                      _gotoArtistSearchScreen(context);
                    },
                    child: Text(
                      'Discover artists',
                      style: TextStyle(
                        height: 1.5,
                        fontSize: 16.0,
                        fontWeight: FontWeight.normal,
                        color: context.colors.accentColor,
                      ),
                    ),
                  ),
                ),
                TextSpan(
                  text: ' on PortraitMode and explore other peoples profiles.',
                  style: TextStyle(
                    height: 1.5,
                    fontSize: 16.0,
                    fontWeight: FontWeight.normal,
                    color: context.colors.brandColorAlt,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _gotoArtistSearchScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SearchScreenActive(initialIndex: 3),
      ),
    );
  }
}
