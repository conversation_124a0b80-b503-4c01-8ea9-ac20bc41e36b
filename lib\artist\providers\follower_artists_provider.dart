import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

final class FollowerArtistIdsNotifier extends IdListNotifier {}

final followerArtistIdsProvider =
    NotifierProvider.autoDispose<FollowerArtistIdsNotifier, List<int>>(
      FollowerArtistIdsNotifier.new,
    );

final followerArtistsProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  final ids = ref.watch(followerArtistIdsProvider);
  final store = ref.watch(artistStoreProvider);
  final items = <ArtistData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing follower artists's reactivity.
/// This is the recommended way to manage follower artists.
final class FollowerArtistsReactiveService {
  const FollowerArtistsReactiveService(this.ref);

  final Ref ref;

  /// Get follower artists list
  List<ArtistData> getAll() {
    return ref.read(followerArtistsProvider);
  }

  /// Get follower artist IDs
  List<int> getAllIds() {
    return ref.read(followerArtistIdsProvider);
  }

  /// Add a new follower artist (adds to both global store and follower list)
  void addItem(ArtistData artist) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItem(artist);

    // Add to follower artists list
    ref.read(followerArtistIdsProvider.notifier).addItem(artist.id);
  }

  /// Add multiple follower artists
  void addItems(List<ArtistData> artists) {
    if (artists.isEmpty) return;

    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Add to follower artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(followerArtistIdsProvider.notifier).addItems(artistIds);
  }

  /// Remove a follower artist
  void remove(int artistId) {
    // Remove from follower artists list
    ref.read(followerArtistIdsProvider.notifier).removeItem(artistId);
  }

  /// Remove multiple follower artists
  void removeItems(List<int> artistIds) {
    if (artistIds.isEmpty) return;

    // Remove from follower artists list
    ref.read(followerArtistIdsProvider.notifier).removeItems(artistIds);
  }

  /// Check if an artist is being followed
  bool isFollower(int artistId) {
    return ref.read(followerArtistIdsProvider.notifier).hasItem(artistId);
  }

  // Toggle follower status for an artist
  void toggleFollower(int artistId) {
    if (isFollower(artistId)) {
      remove(artistId);
    } else {
      // Get artist from store and add to follower if it exists
      final artist = ref.read(artistStoreProvider.notifier).getItem(artistId);
      if (artist != null) {
        addItem(artist);
      }
    }
  }

  // Update an artist in the store (automatically reflects in follower list)
  void updateItem(ArtistData updatedArtist) {
    ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
  }

  // Update multiple artists in the store
  void updateItems(List<ArtistData> updatedArtists) {
    ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
  }

  // Replace all follower artists
  void replaceAll(List<ArtistData> artists) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Replace follower artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(followerArtistIdsProvider.notifier).replaceAll(artistIds);
  }

  // Clear all follower artists
  void clear() {
    // Clear the follower artists list
    ref.read(followerArtistIdsProvider.notifier).clear();
  }

  // Get follower artists count
  int get followerCount => ref.read(followerArtistIdsProvider.notifier).length;

  // Check if follower list is empty
  bool get isEmpty => ref.read(followerArtistIdsProvider.notifier).isEmpty;

  // Check if follower list is not empty
  bool get isNotEmpty =>
      ref.read(followerArtistIdsProvider.notifier).isNotEmpty;
}

/// Provider for the FollowerArtistsReactiveService.
final followerArtistsReactiveServiceProvider =
    Provider.autoDispose<FollowerArtistsReactiveService>((ref) {
      return FollowerArtistsReactiveService(ref);
    });
