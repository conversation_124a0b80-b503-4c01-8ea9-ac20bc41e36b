import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_detail_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_service.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_categories.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_description.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_frame.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_metadata.dart';

class PhotoDetailByIdScreen extends ConsumerStatefulWidget {
  final int photoId;
  final bool isPhotoDetail;

  const PhotoDetailByIdScreen({
    super.key,
    required this.photoId,
    this.isPhotoDetail = true,
  });

  @override
  PhotoDetailByIdScreenState createState() => PhotoDetailByIdScreenState();
}

class PhotoDetailByIdScreenState extends ConsumerState<PhotoDetailByIdScreen> {
  final _scrollController = ScrollController();
  late final _photoDescriptionKey = GlobalKey();

  final PhotoService _photoService = PhotoService();
  final _commentListService = CommentListService();
  final _commentFieldController = TextEditingController();
  final _focusNode = FocusNode();

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _pageIndex = 0;
  bool _loadMoreEndReached = false;

  final _isFetchingCommentsNotifier = ValueNotifier(false);
  final _isFetchingPhotoNotifier = ValueNotifier(true);
  final ValueNotifier<Map<int, GlobalKey>> _activeCommentKeysNotifier =
      ValueNotifier({});

  int? _activeCommentId;

  final double _sectionSpacing = 20.0;

  late final int _profileId;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;

    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getPhotoDetail();
    });
  }

  @override
  void dispose() {
    _activeCommentKeysNotifier.value.clear();

    _commentFieldController.dispose();
    _focusNode.dispose();
    _scrollController.dispose();
    _isFetchingCommentsNotifier.dispose();
    _isFetchingPhotoNotifier.dispose();
    _activeCommentKeysNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingCommentsNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _loadMore(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final PhotoData? photo = ref.watch(photoProvider(widget.photoId));

    return Scaffold(
      body: SafeArea(
        child: ValueListenableBuilder(
          valueListenable: _isFetchingPhotoNotifier,
          builder: (context, isFetchingPhoto, child) {
            if (isFetchingPhoto) {
              return _buildLoadingWidget();
            }

            if (photo == null) {
              return _buildPhotoNotFoundWidget();
            }

            return _buildPhotoDetailScreen(photo);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: SizedBox(
        width: 24.0,
        height: 24.0,
        child: CircularProgressIndicator(
          color: context.colors.borderColor,
          strokeWidth: 3.0,
        ),
      ),
    );
  }

  Widget _buildPhotoNotFoundWidget() {
    return Column(
      children: [
        PmAppBar(
          titleText: 'Oops..',
          useLogo: false,
          automaticallyImplyLeading: true,
          actions: const [],
        ),
        const Expanded(
          child: Center(
            child: Text('Photo not found', style: TextStyle(fontSize: 16.0)),
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoDetailScreen(PhotoData photo) {
    bool hasDescription = photo.description.isNotEmpty;

    final List<CommentData> commentList = ref.watch(
      commentListProvider(widget.photoId),
    );

    return Column(
      children: [
        Expanded(
          child: RefreshIndicator(
            color: context.colors.brandColor,
            elevation: 0.0,
            onRefresh: () => _handleRefresh(photo),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                PmSliverAppBar(
                  scrollController: _scrollController,
                  titleText: widget.isPhotoDetail
                      ? "Photo details"
                      : "Comments",
                  useLogo: false,
                  automaticallyImplyLeading: true,
                  actions: const [],
                ),
                if (widget.isPhotoDetail)
                  SliverToBoxAdapter(
                    child: Align(
                      alignment: Alignment.topCenter,
                      child: Container(
                        constraints: const BoxConstraints(maxWidth: 768.0),
                        child: PhotoDetailFrame(photo: photo),
                      ),
                    ),
                  ),
                if (hasDescription)
                  SliverToBoxAdapter(
                    child: PhotoDetailDescription(
                      key: _photoDescriptionKey,
                      photo: photo,
                      padding: EdgeInsets.only(
                        right: ScreenStyleConfig.horizontalPadding,
                        left: ScreenStyleConfig.horizontalPadding,
                        top: _sectionSpacing,
                      ),
                      contentToDividerGap: _sectionSpacing,
                    ),
                  ),
                if (widget.isPhotoDetail)
                  SliverToBoxAdapter(
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 768.0),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return PhotoMetadata(
                            photo: photo,
                            padding: EdgeInsets.only(
                              left: ScreenStyleConfig.horizontalPadding,
                              right: ScreenStyleConfig.horizontalPadding,
                              top: _sectionSpacing,
                            ),
                            contentToDividerGap: _sectionSpacing,
                          );
                        },
                      ),
                    ),
                  ),
                if (widget.isPhotoDetail && photo.categories.isNotEmpty)
                  SliverToBoxAdapter(
                    child: PhotoCategories(
                      categoryIds: photo.categories,
                      padding: EdgeInsets.only(
                        top: _sectionSpacing,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      contentToDividerGap: _sectionSpacing,
                    ),
                  ),
                if (widget.isPhotoDetail)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.only(
                        top: _sectionSpacing,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: const Text(
                        "Comments",
                        style: TextStyle(
                          fontSize: 15.0,
                          // fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                const SliverToBoxAdapter(
                  child: SizedBox(height: LayoutConfig.contentTopGap),
                ),
                SliverList.builder(
                  itemCount: commentList.length,
                  itemBuilder: (BuildContext context, int index) {
                    double marginBottom = 12.0;

                    final bool isOwnComment =
                        commentList[index].authorId == _profileId;

                    return CommentListItem(
                      key: ValueKey('comment_${commentList[index].id}'),
                      commentFieldController: _commentFieldController,
                      commentFieldFocusNode: _focusNode,
                      comment: commentList[index],
                      isOwnComment: isOwnComment,
                      padding: EdgeInsets.only(bottom: marginBottom),
                      onEditCommentTap: isOwnComment
                          ? () {
                              final int commentId = commentList[index].id;

                              _activeCommentId = commentId;

                              final Map<int, GlobalKey> activeCommentKeys =
                                  Map.from(_activeCommentKeysNotifier.value);

                              activeCommentKeys[commentId] = GlobalKey();

                              _activeCommentKeysNotifier.value =
                                  activeCommentKeys;

                              handleEditCommentTap(
                                ref: ref,
                                comment: commentList[index],
                                commentFieldController: _commentFieldController,
                                commentFieldFocusNode: _focusNode,
                              );
                            }
                          : null,
                      onDeleteCommentTap: isOwnComment
                          ? () async {
                              final int commentId = commentList[index].id;
                              final GlobalKey? targetKey =
                                  _activeCommentKeysNotifier.value[commentId];

                              if (targetKey == null) {
                                return;
                              }

                              DeleteCommentUtil(
                                context: context,
                                ref: ref,
                                photoId: photo.id,
                                commentToDelete: commentList[index],
                                targetKey: targetKey,
                              ).handleDeleteEvent();
                            }
                          : null,
                    );
                  },
                ),
                SliverToBoxAdapter(
                  child: ValueListenableBuilder(
                    valueListenable: _isFetchingCommentsNotifier,
                    builder: (context, isFetching, child) {
                      if (isFetching && !_loadMoreEndReached) {
                        return Container(
                          padding: const EdgeInsets.all(16.0),
                          alignment: Alignment.center,
                          child: CircularProgressIndicator(
                            color: context.colors.baseColorAlt,
                          ),
                        );
                      }

                      if (!isFetching &&
                          _loadMoreEndReached &&
                          commentList.isEmpty) {
                        return Container(
                          padding: const EdgeInsets.all(16.0),
                          alignment: Alignment.center,
                          child: Text(
                            'No comments found',
                            style: TextStyle(
                              color: context.colors.primarySwatch[300],
                            ),
                          ),
                        );
                      }

                      return SizedBox.shrink();
                    },
                  ),
                ),
                if (commentList.isEmpty)
                  const SliverToBoxAdapter(child: SizedBox(height: 30.0)),
              ],
            ),
          ),
        ),
        ValueListenableBuilder(
          valueListenable: _activeCommentKeysNotifier,
          builder: (context, activeCommentKeys, child) {
            return CommentForm(
              photoId: photo.id,
              fieldController: _commentFieldController,
              focusNode: _focusNode,
              photoDescriptionKey: _photoDescriptionKey,
              activeCommentKey: activeCommentKeys[_activeCommentId],
            );
          },
        ),
      ],
    );
  }

  Future<void> _getPhotoDetail() async {
    _isFetchingPhotoNotifier.value = true;

    PhotoDetailResponse response = await _photoService.find(widget.photoId);

    if (!mounted) return;

    if (response.data != null) {
      ref.read(photoStoreProvider.notifier).updateItem(response.data!);
    }

    _isFetchingPhotoNotifier.value = false;

    if (_canLoadMore()) _loadMore(false);
  }

  Future<void> _handleRefresh(PhotoData photo) async {
    _loadMoreEndReached = false;
    _pageIndex = 0;
    ref.read(commentActivityProvider.notifier).reset();
    _loadMore(true);
  }

  Future<void> _loadMore(bool isRefresh) async {
    _isFetchingCommentsNotifier.value = true;

    final CommentListResponse response = await _commentListService.fetch(
      isRefreshAction: isRefresh,
      postId: widget.photoId,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handleCommentListResponse(response, isRefresh);
    _isFetchingCommentsNotifier.value = false;
  }

  void _handleCommentListResponse(
    CommentListResponse response,
    bool isRefresh,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    final photoCommentService = ref.read(photoCommentReactiveServiceProvider);

    final isFirstLoad = _pageIndex == 0 || _pageIndex == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;

      if (isRefresh || isFirstLoad) {
        photoCommentService.clearPhotoComments(widget.photoId);
      }

      return;
    }

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _pageIndex++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      photoCommentService.replaceAllComments(widget.photoId, response.data);
    } else {
      photoCommentService.prependComments(widget.photoId, response.data);
    }
  }
}
