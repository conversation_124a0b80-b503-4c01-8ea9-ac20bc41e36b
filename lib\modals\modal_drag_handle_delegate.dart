import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';

class ModalDragHandleDelegate extends SliverPersistentHeaderDelegate {
  const ModalDragHandleDelegate();

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return const ModalDragHandle();
  }

  @override
  double get maxExtent =>
      BottomSheetConfig.dragHandleHeight +
      BottomSheetConfig.dragHandleMarginTop +
      BottomSheetConfig.dragHandleMarginBottom;

  @override
  double get minExtent =>
      BottomSheetConfig.dragHandleHeight +
      BottomSheetConfig.dragHandleMarginTop +
      BottomSheetConfig.dragHandleMarginBottom;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => false;
}
