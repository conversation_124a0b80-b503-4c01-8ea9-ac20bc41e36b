// profile_data.dart

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/app/config/config.dart';

@immutable
final class ProfileData {
  final int id;
  final String nicename;
  final String role;
  final String email;
  final String firstName;
  final String lastName;
  final String displayName;
  final String description;
  final String website;
  final String instagram;
  final String profileUrl;
  final String avatarUrl;
  final String location;
  final String latestPhotoUrl;
  final int totalPhotos;
  final String camera;
  final String focalLength;
  final bool isFollowing;
  final int totalFollowing;
  final int totalFollowers;
  final String membershipType;

  const ProfileData({
    this.id = 0,
    this.nicename = '',
    this.role = '',
    this.email = '',
    this.website = '',
    this.instagram = '',
    this.profileUrl = '',
    this.avatarUrl =
        'https://portraitmode.io/wp-content/themes/page-builder-framework-child/images/portraitmode-logo-circle-256x256.png',
    this.firstName = '',
    this.lastName = '',
    this.displayName = '',
    this.description = '',
    this.location = '',
    this.latestPhotoUrl = '',
    this.totalPhotos = 0,
    this.camera = '',
    this.focalLength = '',
    this.isFollowing = false,
    this.totalFollowing = 0,
    this.totalFollowers = 0,
    this.membershipType = '',
  });

  ProfileData copyWith({
    int? id,
    String? nicename,
    String? role,
    String? email,
    String? firstName,
    String? lastName,
    String? displayName,
    String? description,
    String? website,
    String? instagram,
    String? profileUrl,
    String? avatarUrl,
    String? location,
    String? latestPhotoUrl,
    int? totalPhotos,
    String? camera,
    String? focalLength,
    bool? isFollowing,
    int? totalFollowing,
    int? totalFollowers,
    String? membershipType,
  }) {
    return ProfileData(
      id: id ?? this.id,
      nicename: nicename ?? this.nicename,
      role: role ?? this.role,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      website: website ?? this.website,
      instagram: instagram ?? this.instagram,
      profileUrl: profileUrl ?? this.profileUrl,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      location: location ?? this.location,
      latestPhotoUrl: latestPhotoUrl ?? this.latestPhotoUrl,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      camera: camera ?? this.camera,
      focalLength: focalLength ?? this.focalLength,
      isFollowing: isFollowing ?? this.isFollowing,
      totalFollowing: totalFollowing ?? this.totalFollowing,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      membershipType: membershipType ?? this.membershipType,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nicename': nicename,
      'role': role,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'displayName': displayName,
      'profileUrl': profileUrl,
      'description': description,
      'website': website,
      'instagram': instagram,
      'avatarUrl': avatarUrl,
      'location': location,
      'latestPhotoUrl': latestPhotoUrl,
      'totalPhotos': totalPhotos,
      'camera': camera,
      'focalLength': focalLength,
      'isFollowing': isFollowing,
      'totalFollowing': totalFollowing,
      'totalFollowers': totalFollowers,
      'membershipType': membershipType,
    };
  }

  factory ProfileData.fromMap(Map<String, dynamic> data) {
    return ProfileData(
      id: data['id'] ?? 0,
      nicename: data['nicename'] ?? '',
      role: data.containsKey('role') ? data['role'] : 'subscriber',
      email: data['email'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      displayName: data['displayName'] ?? '',
      description: data['description'] ?? '',
      website: data['website'] ?? '',
      instagram: data['instagram'] ?? '',
      profileUrl: data['profileUrl'] ?? '',
      avatarUrl: data['avatarUrl'] ?? AvatarConfig.defaultAvatar,
      location: data['location'] ?? '',
      latestPhotoUrl: data['latestPhotoUrl'] ?? '',
      totalPhotos: data['totalPhotos'] ?? 0,
      camera: data['camera'] ?? '',
      focalLength: data['focalLength'] ?? '',
      isFollowing: data['isFollowing'] ?? false,
      totalFollowing: data['totalFollowing'] ?? 0,
      totalFollowers: data['totalFollowers'] ?? 0,
      membershipType: data['membershipType'] ?? '',
    );
  }

  factory ProfileData.fromJson(String source) =>
      ProfileData.fromMap(json.decode(source));

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProfileData) return false;

    return other.id == id &&
        other.nicename == nicename &&
        other.role == role &&
        other.email == email &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.displayName == displayName &&
        other.description == description &&
        other.website == website &&
        other.instagram == instagram &&
        other.profileUrl == profileUrl &&
        other.avatarUrl == avatarUrl &&
        other.location == location &&
        other.latestPhotoUrl == latestPhotoUrl &&
        other.totalPhotos == totalPhotos &&
        other.camera == camera &&
        other.focalLength == focalLength &&
        other.isFollowing == isFollowing &&
        other.totalFollowing == totalFollowing &&
        other.totalFollowers == totalFollowers &&
        other.membershipType == membershipType;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      id,
      nicename,
      role,
      email,
      firstName,
      lastName,
      displayName,
      description,
      website,
      instagram,
      profileUrl,
      avatarUrl,
      location,
      latestPhotoUrl,
      totalPhotos,
      camera,
      focalLength,
      isFollowing,
      totalFollowing,
      totalFollowers,
      membershipType,
    ]);
  }
}
