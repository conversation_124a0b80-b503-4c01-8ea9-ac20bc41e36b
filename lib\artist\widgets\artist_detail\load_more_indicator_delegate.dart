import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class LoadMoreIndicatorDelegate extends SliverPersistentHeaderDelegate {
  final bool? isLoadingMore;
  final ValueNotifier<bool>? isLoadingMoreNotifier;

  LoadMoreIndicatorDelegate({this.isLoadingMore, this.isLoadingMoreNotifier});

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    if (isLoadingMoreNotifier != null) {
      return Container(
        height: 2.7,
        color: context.colors.baseColorAlt,
        child: ValueListenableBuilder(
          valueListenable: isLoadingMoreNotifier!,
          builder: (context, isLoadingMore, child) {
            if (isLoadingMore) {
              return Container(
                color: isLoadingMore
                    ? context.colors.baseColorAlt
                    : Colors.transparent,
                child: LinearProgressIndicator(
                  color: context.colors.baseColorAlt,
                ),
              );
            }

            return const SizedBox.shrink();
          },
        ),
      );
    }

    if (isLoadingMore != null) {
      return Container(
        height: 2.7,
        color: isLoadingMore!
            ? context.colors.baseColorAlt
            : Colors.transparent,
        child: isLoadingMore!
            ? LinearProgressIndicator(color: context.colors.baseColorAlt)
            : SizedBox.shrink(),
      );
    }

    return Container(color: Colors.transparent, height: 2.7);
  }

  @override
  double get maxExtent => 2.7;

  @override
  double get minExtent => 2.7;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}
