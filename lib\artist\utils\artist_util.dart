import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/follow_response.dart';
import 'package:portraitmode/artist/misc.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';

List<int> artistListToIdList(List<ArtistData> artistList) {
  return artistList.map((artist) => artist.id).toList();
}

void handleFollowStatusChanged({
  required WidgetRef ref,
  required FollowActionType action,
  required ArtistData artist,
  FollowResponseData? data,
}) {
  if (data == null) {
    ref
        .read(artistStoreProvider.notifier)
        .setIsFollowing(artist.id, action == FollowActionType.follow);

    return;
  }

  // Update the artist data.
  ref
      .read(artistStoreProvider.notifier)
      .updateItem(
        artist.copyWith(
          isFollowing: action == FollowActionType.follow,
          totalFollowers: data.artistTotalFollowers,
          totalFollowing: data.artistTotalFollowing,
        ),
      );

  final myProfileId = LocalUserService.userId;

  final myProfile = myProfileId != null
      ? ref.read(artistStoreProvider.notifier).getItem(myProfileId)
      : null;

  // Update my profile data.
  if (myProfile != null) {
    ref
        .read(artistStoreProvider.notifier)
        .updateItem(
          myProfile.copyWith(
            totalFollowers: data.followerTotalFollowers,
            totalFollowing: data.followerTotalFollowing,
          ),
        );
  }
}
