// upload_photo_modal.dart

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_place/google_place.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_chip.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/widgets/categories_picker.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/location_picker/location_picker.dart';
import 'package:portraitmode/modals/modal_drag_handle_delegate.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/dto/photo_upload_data.dart';
import 'package:portraitmode/photo/http_responses/upload_photo_response.dart';
import 'package:portraitmode/photo/providers/latest_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/upload_photo_service.dart';
import 'package:portraitmode/photo/widgets/photo_upload/photo_picker.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class UploadPhotoModal extends ConsumerStatefulWidget {
  const UploadPhotoModal({super.key});

  @override
  UploadPhotoModalState createState() => UploadPhotoModalState();
}

class UploadPhotoModalState extends ConsumerState<UploadPhotoModal> {
  final _formKey = GlobalKey<FormState>();
  final GooglePlace _googlePlace = GooglePlace(GoogleConfig.mapApiKey);

  final _descriptionFieldController = TextEditingController();
  final _locationFieldController = TextEditingController();

  double? _locationLat;
  double? _locationLng;
  String _locationAddress = '';

  final ValueNotifier<List<CategoryData>> _selectedCategoriesNotifier =
      ValueNotifier([]);
  final ValueNotifier<File?> _selectedPhotoNotifier = ValueNotifier(null);
  final _needsFeedbackNotifier = ValueNotifier(false);

  late Function? _cleanPreview;

  @override
  void initState() {
    super.initState();

    _descriptionFieldController.text = globalPhotoUploadData.description ?? '';
    _locationFieldController.text = globalPhotoUploadData.address;

    _locationLat = globalPhotoUploadData.lat;
    _locationLng = globalPhotoUploadData.lng;
    _locationAddress = globalPhotoUploadData.address;

    _selectedCategoriesNotifier.value = globalPhotoUploadData.categories;
    _selectedPhotoNotifier.value = globalPhotoUploadData.photo;
    _needsFeedbackNotifier.value = globalPhotoUploadData.needsFeedback;
  }

  @override
  void dispose() {
    _descriptionFieldController.dispose();
    _locationFieldController.dispose();
    _selectedCategoriesNotifier.dispose();
    _selectedPhotoNotifier.dispose();
    _needsFeedbackNotifier.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final keyboardHeight = MediaQuery.viewInsetsOf(context).bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // Calculate dynamic sizes based on keyboard state
    final double initialChildSize = isKeyboardVisible ? 0.8 : 0.6;

    return DraggableScrollableSheet(
      maxChildSize: BottomSheetConfig.maxChildSize,
      initialChildSize: initialChildSize,
      expand: false,
      snap: true,
      snapSizes: isKeyboardVisible
          ? [0.8, BottomSheetConfig.maxChildSize]
          : [0.6, BottomSheetConfig.maxChildSize],
      builder: (BuildContext context, ScrollController scrollController) {
        return SafeArea(
          child: Form(
            key: _formKey,
            child: CustomScrollView(
              controller: scrollController,
              slivers: [
                SliverPersistentHeader(
                  pinned: true,
                  floating: false,
                  delegate: ModalDragHandleDelegate(),
                ),
                SliverList(
                  delegate: SliverChildListDelegate([
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: ValueListenableBuilder(
                        valueListenable: _selectedPhotoNotifier,
                        builder: (context, selectedPhoto, child) {
                          return PhotoPicker(
                            onInit: (cleanPreview) {
                              _cleanPreview = cleanPreview;
                            },
                            onChange: (photo) {
                              _selectedPhotoNotifier.value = photo;
                              globalPhotoUploadData.photo = photo;
                            },
                            defaultPhoto: selectedPhoto,
                          );
                        },
                      ),
                    ),

                    // Location field
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _locationFieldController,
                        readOnly: true,
                        labelText: "Photo location",
                        validator: FieldValidators.locationValidator.call,
                        onTap: () async {
                          _openLocationPicker(context);
                        },
                      ),
                    ),

                    // Caption field with improved focus handling
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _descriptionFieldController,
                        textCapitalization: TextCapitalization.sentences,
                        onChanged: _handleDescriptionChanged,
                        labelText: "Caption",
                      ),
                    ),

                    _buildRequestFeedbackField(),

                    // Categories section
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4.0),
                          border: Border.all(
                            color: context.isDarkMode
                                ? AppColorsCache.dark().baseColorAlt
                                : AppColorsCache.light().baseColor,
                            width: 1.0,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => CategoriesPicker(
                                      selectedCategories:
                                          _selectedCategoriesNotifier.value,
                                      onClose: (cats) {
                                        FocusScope.of(context).unfocus();
                                        _selectedCategoriesNotifier.value =
                                            cats;
                                        globalPhotoUploadData.categories = cats;
                                      },
                                    ),
                                  ),
                                );
                              },
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    Ionicons.add_circle,
                                    color: context.colors.accentColor,
                                    size: 20.0,
                                  ),
                                  const SizedBox(width: 4.0),
                                  Text(
                                    "Add categories",
                                    style: TextStyle(
                                      color: context.colors.accentColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            ValueListenableBuilder(
                              valueListenable: _selectedCategoriesNotifier,
                              builder: (context, selectedCategories, child) {
                                if (selectedCategories.isEmpty) {
                                  return const SizedBox.shrink();
                                }

                                return Padding(
                                  padding: const EdgeInsets.only(
                                    left: 4.0,
                                    right: 4.0,
                                    bottom: 8.0,
                                  ),
                                  child: Wrap(
                                    spacing: 8.0,
                                    runSpacing: 8.0,
                                    children: selectedCategories
                                        .map(
                                          (cat) => PmChip(
                                            label: Text(cat.name),
                                            onDeleted: () {
                                              _selectedCategoriesNotifier.value
                                                  .remove(cat);
                                            },
                                          ),
                                        )
                                        .toList(),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Submit button with proper spacing
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                        bottom: 20.0, // Extra bottom padding
                      ),
                      child: SubmitButton(
                        buttonText: "Submit photo",
                        width: double.infinity,
                        height: 40.0,
                        fontWeight: FontWeight.w600,
                        onPressed: _handleSubmission,
                      ),
                    ),
                  ]),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Rest of the methods remain the same...
  Widget _buildRequestFeedbackField() {
    if (globalFeedbackTokensAmount < 1) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder(
      valueListenable: _needsFeedbackNotifier,
      builder: (context, needsFeedback, child) {
        WidgetStateProperty<Color>? checkboxFillColor = !needsFeedback
            ? WidgetStateProperty.all<Color>(context.colors.scaffoldColor)
            : WidgetStateProperty.all<Color>(context.colors.accentColor);

        final Color checkboxBorderColor = context.isDarkMode
            ? AppColorsCache.dark().darkerGreyColor
            : AppColorsCache.light().greyColor;

        return Padding(
          padding: const EdgeInsets.only(
            bottom: 8.0,
            left: 4.0,
            right: ScreenStyleConfig.horizontalPadding,
          ),
          child: InkWell(
            onTap: _handleRequestFeedbackTap,
            child: Row(
              children: [
                Checkbox(
                  value: needsFeedback,
                  visualDensity: VisualDensity.compact,
                  onChanged: null,
                  activeColor: context.colors.accentColor,
                  checkColor: context.colors.baseColor,
                  fillColor: checkboxFillColor,
                  side: BorderSide(color: checkboxBorderColor, width: 1.2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                ),
                Text(
                  "Request feedback",
                  style: TextStyle(
                    fontSize: 15.0,
                    color: context.colors.brandColorAlt,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleDescriptionChanged(String value) {
    globalPhotoUploadData.description = value;
  }

  void _openLocationPicker(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return LocationPicker(onChanged: _handleLocationChanged);
        },
      ),
    );
  }

  void _handleLocationChanged(AutocompletePrediction prediction) async {
    _locationFieldController.text = prediction.description ?? _locationAddress;

    _locationAddress = prediction.description ?? _locationAddress;
    globalPhotoUploadData.address = _locationAddress;

    DetailsResponse? response = await _getDetailedResult(prediction);
    if (response == null) return;

    DetailsResult? result = response.result;
    if (result == null) return;
    if (result.formattedAddress == null) return;

    _locationFieldController.text =
        result.formattedAddress ?? _locationFieldController.text;

    Geometry? geometry = result.geometry;
    if (geometry == null) return;

    Location? location = geometry.location;
    if (location == null) return;
    if (location.lat == null || location.lng == null) return;

    _locationAddress = result.formattedAddress ?? _locationAddress;
    _locationLat = location.lat;
    _locationLng = location.lng;

    globalPhotoUploadData.address = _locationAddress;
    globalPhotoUploadData.lat = _locationLat;
    globalPhotoUploadData.lng = _locationLng;
  }

  Future<DetailsResponse?> _getDetailedResult(
    AutocompletePrediction prediction,
  ) async {
    if (prediction.placeId == null) return null;

    DetailsResponse? result = await _googlePlace.details.get(
      prediction.placeId ?? '',
    );

    return result;
  }

  void _handleRequestFeedbackTap() {
    final bool checked = !_needsFeedbackNotifier.value;

    _needsFeedbackNotifier.value = checked;
    globalPhotoUploadData.needsFeedback = checked;
  }

  final _uploadPhotoService = UploadPhotoService();

  Future<void> _handleSubmission() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedPhotoNotifier.value == null) return;

    Uint8List photoBytes = await _selectedPhotoNotifier.value!.readAsBytes();

    UploadPhotoResponse response = await _uploadPhotoService.submit(
      photoBytes: photoBytes,
      address: _locationAddress,
      lat: _locationLat.toString(),
      lng: _locationLng.toString(),
      description: _descriptionFieldController.text,
      categoryIds: _selectedCategoriesNotifier.value
          .map((cat) => cat.id)
          .toList(),
      needsFeedback: _needsFeedbackNotifier.value,
    );

    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
        return;
      }

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Text(response.message),
            actions: <Widget>[
              TextButton(
                child: const Text("Close"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );

      return;
    }

    globalPhotoUploadData = PhotoUploadData();

    PhotoData data = response.data ?? const PhotoData();

    ref.read(photoStoreProvider.notifier).addItem(data, updateIfExists: true);
    ref.read(latestPhotoIdsProvider.notifier).addItem(data.id, reorder: true);

    /// After uploading a photo (and the status is success),
    /// it will redirect to profile screen.
    /// That means, the initState() of profile screen will be called.
    /// That means, the load more related state will be reset.
    ///
    /// So, we need to reset "all-photos" album provider
    /// instead of adding the newly uploaded photo to the provider.
    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      AutoDisposeNotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
      provider,
    ) {
      if (albumSlug == 'all-photos') {
        ref.read(provider.notifier).clear();
      }
    });

    ref.read(myAlbumProvider.notifier).incrementTotalPhotos('all-photos');
    _cleanPreview?.call();

    _locationFieldController.text = '';
    _descriptionFieldController.text = '';

    _selectedCategoriesNotifier.value = [];
    _selectedPhotoNotifier.value = null;

    final userData = LocalUserService.get();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => ArtistDetailScreen(
            isOwnProfile: true,
            partialData: ArtistPartialData(
              id: userData.userId ?? 0,
              nicename: userData.nicename ?? '',
              displayName: userData.displayName ?? '',
              profileUrl: userData.profileUrl ?? '',
              avatarUrl: userData.avatarUrl ?? '',
              membershipType: userData.membershipType ?? '',
            ),
          ),
        ),
      );
    }
  }
}
