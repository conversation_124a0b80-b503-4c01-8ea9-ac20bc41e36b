import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class FollowingPhotoIdsNotifier extends IdListNotifier {}

final followingPhotoIdsProvider =
    NotifierProvider.autoDispose<FollowingPhotoIdsNotifier, List<int>>(
      FollowingPhotoIdsNotifier.new,
    );

final followingPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(followingPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing following photos reactivity.
///
/// This is the recommended way to manage latest photos.
final class FollowingPhotosReactiveService {
  const FollowingPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get local photo list
  List<PhotoData> getAll() {
    return ref.read(followingPhotosProvider);
  }

  /// Get local id list
  List<int> getAllIds() {
    return ref.read(followingPhotoIdsProvider);
  }

  /// Add a new photo (adds to both global store and local ids)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to local id list
    ref.read(followingPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(followingPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a local photo
  void remove(int photoId) {
    ref.read(followingPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple local photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from local id list
    ref.read(followingPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in local id list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all local photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(followingPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  // Clear the local id list
  void clear() {
    ref.read(followingPhotoIdsProvider.notifier).clear();
  }

  /// Get local id list count
  int get count => ref.read(followingPhotoIdsProvider.notifier).length;

  /// Check if local id list is empty
  bool get isEmpty => ref.read(followingPhotoIdsProvider.notifier).isEmpty;

  /// Check if local id list is not empty
  bool get isNotEmpty =>
      ref.read(followingPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the FollowingPhotosReactiveService.
final followingPhotosReactiveServiceProvider =
    Provider.autoDispose<FollowingPhotosReactiveService>((ref) {
      return FollowingPhotosReactiveService(ref);
    });
