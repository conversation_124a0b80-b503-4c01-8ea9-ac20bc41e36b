import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';

final class MyAlbumNotifier extends AutoDisposeNotifier<List<AlbumData>> {
  final Map<String, int> _slugIndexCache = {};

  @override
  List<AlbumData> build() {
    ref.keepAlive();
    return <AlbumData>[];
  }

  void _updateState(List<AlbumData> newState) {
    if (!listEquals(state, newState)) {
      state = newState;
      _rebuildIndexCache();
    }
  }

  void _rebuildIndexCache() {
    _slugIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _slugIndexCache[state[i].slug] = i;
    }
  }

  AlbumData? getItem(String slug) {
    final index = _slugIndexCache[slug];
    if (index == null || index >= state.length) return null;
    return state[index];
  }

  int getIndex(String slug) {
    return _slugIndexCache[slug] ?? -1;
  }

  bool hasItem(String slug) {
    return _slugIndexCache.containsKey(slug);
  }

  void addItem(AlbumData newItem) {
    if (hasItem(newItem.slug)) return;
    final updated = [...state, newItem];
    _updateState(updated);
  }

  void addItems(List<AlbumData> newItems) {
    final existingSlugs = _slugIndexCache.keys.toSet();
    final filtered = newItems.where(
      (item) => !existingSlugs.contains(item.slug),
    );
    if (filtered.isEmpty) return;

    final updated = [...state, ...filtered];
    _updateState(updated);
  }

  void updateItem(AlbumData newItem) {
    final index = _slugIndexCache[newItem.slug];
    if (index == null || index >= state.length) return;

    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  void incrementTotalPhotos(String albumSlug) {
    final album = getItem(albumSlug);
    if (album == null) return;

    final updatedAlbum = album.copyWith(totalPhotos: album.totalPhotos + 1);
    updateItem(updatedAlbum);
  }

  void decrementTotalPhotos(String albumSlug) {
    final album = getItem(albumSlug);
    if (album == null) return;

    final int totalPhotos = album.totalPhotos - 1;

    final updatedAlbum = album.copyWith(
      totalPhotos: totalPhotos < 0 ? 0 : totalPhotos,
    );

    updateItem(updatedAlbum);
  }

  void removeItem(String slug) {
    if (!_slugIndexCache.containsKey(slug)) return;
    final updated = state.where((item) => item.slug != slug).toList();
    _updateState(updated);
  }

  void replaceAll(List<AlbumData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }
}

final myAlbumProvider =
    NotifierProvider.autoDispose<MyAlbumNotifier, List<AlbumData>>(
      MyAlbumNotifier.new,
    );

/// ⚠️ DO NOT DELETE — THIS GLOBAL VARIABLE IS REQUIRED
///
/// This variable is used to correctly switch to the **last tab** in `MyProfile`
/// after modifying `myAlbumProvider` by adding a new album.
///
/// Why it's needed:
/// - Using `tabController.length - 1` to go to the last tab ends up selecting the **second last tab**
///   because `tabController` is still referencing the **previous state** during the switch.
/// - Using `tabController.length` causes a **crash** due to accessing an index **out of bounds**.
///
/// Alternative approaches fail:
/// - Using a `NotificationListener` in `MyProfile` to catch a custom event and then call
///   `DefaultTabController.of(context)` causes an **error** because the context lacks
///   a `DefaultTabController` ancestor.
/// - Wrapping with a `Builder` avoids the context error, but still causes the issue where
///   the **second last tab** is activated instead of the last.
///
/// A possible fix would be to store the active tab index in `myAlbumProvider`,
/// but that adds unnecessary complexity.
///
/// ✅ Instead, we use this simple global variable as a flag to trigger the correct tab switch.
bool globalMyAlbumShouldSwitchToLastTab = false;
