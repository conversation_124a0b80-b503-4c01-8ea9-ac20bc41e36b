import 'package:flutter/foundation.dart';

class LikeCommentResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final LikeCommentResponseData? data;

  LikeCommentResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory LikeCommentResponse.fromMap(Map<String, dynamic> map) {
    return LikeCommentResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? LikeCommentResponseData.fromMap(map['data'])
          : null,
    );
  }
}

@immutable
class LikeCommentResponseData {
  final bool isLiked;
  final int totalLikes;

  const LikeCommentResponseData({this.isLiked = false, this.totalLikes = 0});

  factory LikeCommentResponseData.fromMap(Map<String, dynamic> map) {
    return LikeCommentResponseData(
      isLiked: map['isLiked'] ?? false,
      totalLikes: map['totalLikes'] ?? 0,
    );
  }

  LikeCommentResponseData copyWith({bool? isLiked, int? totalLikes}) {
    return LikeCommentResponseData(
      isLiked: isLiked ?? this.isLiked,
      totalLikes: totalLikes ?? this.totalLikes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LikeCommentResponseData) return false;

    return other.isLiked == isLiked && other.totalLikes == totalLikes;
  }

  @override
  int get hashCode => Object.hash(isLiked, totalLikes);

  @override
  String toString() {
    return '''
LikeCommentResponseData(
  isLiked: $isLiked,
  totalLikes: $totalLikes,
)
''';
  }
}
