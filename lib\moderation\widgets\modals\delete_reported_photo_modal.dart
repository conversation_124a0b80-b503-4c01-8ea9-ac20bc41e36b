import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle_delegate.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/moderation/utils/reason.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/archived_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class DeleteReportedPhotoModal extends ConsumerStatefulWidget {
  final PhotoData photo;
  final bool isOwnPhoto;
  final Function(int)? onPhotoDeleted;

  const DeleteReportedPhotoModal({
    super.key,
    required this.photo,
    this.isOwnPhoto = false,
    this.onPhotoDeleted,
  });

  @override
  DeleteReportedPhotoModalState createState() =>
      DeleteReportedPhotoModalState();
}

class DeleteReportedPhotoModalState
    extends ConsumerState<DeleteReportedPhotoModal> {
  final ModerationService _moderationService = ModerationService();

  final _selectedReasonNotifier = ValueNotifier('');
  bool _isLoading = false;

  final Map<String, String> _reasons = getModerationDeletionReasons();

  @override
  void dispose() {
    _selectedReasonNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      maxChildSize: BottomSheetConfig.maxChildSize,
      initialChildSize: 0.8,
      expand: false,
      builder: (context, scrollController) {
        return SafeArea(
          child: ValueListenableBuilder(
            valueListenable: _selectedReasonNotifier,
            builder: (context, selectedReason, child) {
              return CustomScrollView(
                controller: scrollController,
                slivers: [
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: ModalDragHandleDelegate(),
                  ),

                  // List of reasons
                  SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final String reason = _reasons.keys.elementAt(index);

                      return ListTile(
                        title: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _reasons[reason]!,
                              style: const TextStyle(fontSize: 14.5),
                            ),
                            if (selectedReason == reason)
                              const SizedBox(width: 5.0),
                            if (selectedReason == reason)
                              Icon(
                                Ionicons.checkmark_circle_sharp,
                                color: context.colors.accentColor,
                                size: 15,
                              ),
                          ],
                        ),
                        onTap: () => _handleOnTap(reason),
                        // dense: true,
                        visualDensity: const VisualDensity(vertical: -3.0),
                      );
                    }, childCount: _reasons.length),
                  ),

                  // Submit button at the bottom, sticks to bottom if list is short
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                        bottom: 16.0,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SubmitButton(
                            buttonText: "Delete & notify",
                            bgColor: context.colors.dangerColor,
                            width: double.infinity,
                            height: 40.0,
                            fontWeight: FontWeight.w600,
                            onPressed: () async => await _handleOnSubmit(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _handleOnTap(String targettedAlbumSlug) async {
    if (_selectedReasonNotifier.value == targettedAlbumSlug) {
      _selectedReasonNotifier.value = '';
    } else {
      _selectedReasonNotifier.value = targettedAlbumSlug;
    }
  }

  Future<void> _handleOnSubmit() async {
    if (_isLoading) return;
    _isLoading = true;

    final BaseResponse response = await _moderationService.deleteReportedPhoto(
      photoId: widget.photo.id,
      reason: _selectedReasonNotifier.value,
    );

    if (!mounted) return;

    if (!response.success) {
      _isLoading = false;

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    _isLoading = false;

    if (widget.isOwnPhoto) {
      ref.read(archivedPhotoIdsProvider.notifier).removeItem(widget.photo.id);

      ref
          .read(myAlbumProvider.notifier)
          .decrementTotalPhotos(widget.photo.album);

      myAlbumPhotoListProviderMap.forEach((
        String albumSlug,
        AutoDisposeNotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>>
        provider,
      ) {
        ref.read(provider.notifier).removeItem(widget.photo.id);
      });
    }

    ref.read(photoStoreProvider.notifier).removeItem(widget.photo.id);

    if (widget.onPhotoDeleted != null) {
      widget.onPhotoDeleted!(widget.photo.id);
    }

    Navigator.of(context).pop();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(response.message)));
  }
}
