{"buildFiles": ["C:\\Users\\<USER>\\fvm\\default\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter\\portraitmode\\android\\app\\.cxx\\RelWithDebInfo\\5q656b30\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter\\portraitmode\\android\\app\\.cxx\\RelWithDebInfo\\5q656b30\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}