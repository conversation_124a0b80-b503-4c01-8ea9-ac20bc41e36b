import 'package:flutter/widgets.dart';

enum SearchScreenItem {
  photoSearchScreen,
  categorySearchScreen,
  cameraSearchScreen,
  artistSearchScreen,
}

enum SearchScreenLoadMoreStatus {
  idle,
  doingFirstLoadMore,
  doingLoadMore,
  loadMoreEndReached,
}

// `SearchLoadingNotification` is used to indicate whether
// a loading indicator should be shown, rather than triggering
// an actual "load more" operation.

@immutable
class SearchLoadingNotification extends Notification {
  final bool value;

  const SearchLoadingNotification(this.value);
}
