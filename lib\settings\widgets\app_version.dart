import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:portraitmode/app/config/colors.dart';

class AppVersion extends StatefulWidget {
  const AppVersion({super.key});

  @override
  AppVersionState createState() => AppVersionState();
}

class AppVersionState extends State<AppVersion> {
  final _versionNotifier = ValueNotifier('');

  @override
  void initState() {
    _getAppVersion();
    super.initState();
  }

  @override
  void dispose() {
    _versionNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _versionNotifier,
      builder: (context, version, child) {
        return Text(
          'PortraitMode v$version',
          style: TextStyle(fontSize: 13, color: context.colors.darkerGreyColor),
        );
      },
    );
  }

  Future<void> _getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _versionNotifier.value = packageInfo.version;
  }
}
