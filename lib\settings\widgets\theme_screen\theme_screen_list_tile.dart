import 'package:flutter/material.dart';

class ThemeScreenListTile extends StatelessWidget {
  final String title;
  final String value;
  final bool selected;

  const ThemeScreenListTile({
    super.key,
    required this.title,
    required this.value,
    this.selected = false,
  });

  @override
  Widget build(BuildContext context) {
    return RadioListTile(
      value: value,
      dense: true,
      visualDensity: const VisualDensity(horizontal: 0.0, vertical: -2.0),
      title: Text(title, style: const TextStyle(fontSize: 16.0)),
    );
  }
}
