import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

class SearchTabbar extends StatefulWidget {
  const SearchTabbar({super.key});

  // @override
  // Size get preferredSize =>
  //     Size.fromHeight(LayoutConfig.bottomNavBarHeight - 3.0);

  @override
  SearchTabbarState createState() => SearchTabbarState();
}

class SearchTabbarState extends State<SearchTabbar>
    with SingleTickerProviderStateMixin {
  final double _screenXPadding = TabbarConfig.screenXPadding;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.sizeOf(context).width;
    final double containerWidth = screenWidth - _screenXPadding;

    final double tabWidth = containerWidth / 4;

    return Container(
      color: context.colors.scaffoldColor,
      padding: EdgeInsets.symmetric(horizontal: _screenXPadding),
      child: TabBar(
        padding: EdgeInsets.zero,
        tabAlignment: TabAlignment.fill,
        labelPadding: EdgeInsets.zero,
        labelColor: context.colors.labelColor,
        unselectedLabelColor: context.colors.primarySwatch[300],
        tabs: [
          Tab(
            child: SizedBox(
              width: tabWidth,
              child: const Text('Photos', textAlign: TextAlign.center),
            ),
          ),
          Tab(
            child: SizedBox(
              width: tabWidth,
              child: const Text('Categories', textAlign: TextAlign.center),
            ),
          ),
          Tab(
            child: SizedBox(
              width: tabWidth,
              child: const Text('Cameras', textAlign: TextAlign.center),
            ),
          ),
          Tab(
            child: SizedBox(
              width: tabWidth,
              child: const Text('Artists', textAlign: TextAlign.center),
            ),
          ),
        ],
      ),
    );
  }
}
