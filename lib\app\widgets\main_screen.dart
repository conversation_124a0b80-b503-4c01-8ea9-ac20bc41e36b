import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/home/<USER>/home_screen.dart';
import 'package:portraitmode/notification/utils/notification_util.dart';
import 'package:portraitmode/notification/widgets/notif_icon.dart';
import 'package:portraitmode/notification/widgets/notifications_screen.dart';
import 'package:portraitmode/photo/widgets/featured_photo_list_screen.dart';
import 'package:portraitmode/photo/widgets/upload_photo_modal.dart';
import 'package:portraitmode/search/widgets/search_screen.dart';

class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({
    super.key,
    this.initialIndex,
    this.searchTabInitialIndex,
    this.customScreen = "",
  });

  final int? initialIndex;
  final int? searchTabInitialIndex;
  final String customScreen;

  @override
  TabsScreenState createState() => TabsScreenState();
}

class TabsScreenState extends ConsumerState<MainScreen> {
  final ValueNotifier<VoidCallback?> _homeScreenRefreshNotifier = ValueNotifier(
    null,
  );

  final ValueNotifier<VoidCallback?> _searchScreenRefreshNotifier =
      ValueNotifier(null);

  final ValueNotifier<VoidCallback?> _featuredPhotosScreenRefreshNotifier =
      ValueNotifier(null);

  late final ValueNotifier<int> _activeTabIndexNotifier;

  late final List<Widget> _screens;
  late final List<BottomNavigationBarItem> _bottomNavBarItems;

  @override
  void initState() {
    super.initState();

    _activeTabIndexNotifier = ValueNotifier(widget.initialIndex ?? 0);

    _screens = [
      HomeScreen(refreshNotifier: _homeScreenRefreshNotifier),
      SearchScreen(refreshNotifier: _searchScreenRefreshNotifier),
      (widget.customScreen.isNotEmpty
          ? _buildCustomScreen()
          : const SizedBox.shrink()),
      FeaturedPhotoListScreen(
        refreshNotifier: _featuredPhotosScreenRefreshNotifier,
      ),
      const SizedBox.shrink(),
    ];

    _bottomNavBarItems = <BottomNavigationBarItem>[
      const BottomNavigationBarItem(
        icon: Icon(Ionicons.home_outline),
        label: "",
      ),
      const BottomNavigationBarItem(
        icon: Icon(Ionicons.search_outline),
        label: "",
      ),

      const BottomNavigationBarItem(
        icon: Icon(Ionicons.trophy_outline),
        label: "",
      ),
      const BottomNavigationBarItem(
        icon: SizedBox(width: 40.0, height: 34.0, child: NotifIcon()),
        label: "",
      ),
    ];
  }

  @override
  void dispose() {
    _homeScreenRefreshNotifier.dispose();
    _searchScreenRefreshNotifier.dispose();
    _featuredPhotosScreenRefreshNotifier.dispose();
    _activeTabIndexNotifier.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    pmLog('🖥️ Building MainScreen');

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        FocusScope.of(context).unfocus();

        pmLog('onPopInvokedWithResult invoked in MainScreen');

        if (_activeTabIndexNotifier.value != 0) {
          _activeTabIndexNotifier.value = 0;
          return;
        }
      },
      child: Scaffold(
        body: SafeArea(
          child: ValueListenableBuilder(
            valueListenable: _activeTabIndexNotifier,
            builder: (context, activeTabIndex, child) {
              return IndexedStack(index: activeTabIndex, children: _screens);
            },
          ),
        ),
        bottomNavigationBar: SafeArea(
          child: Container(
            height: LayoutConfig.bottomNavBarHeight,
            constraints: BoxConstraints(maxWidth: ScreenStyleConfig.maxWidth),
            decoration: BoxDecoration(
              color: context.colors.scaffoldColor,
              border: Border(
                top: BorderSide(
                  color: context.colors.borderColor,
                  width: AppConfig.bottomNavBarBorderWidth,
                ),
              ),
            ),
            child: ValueListenableBuilder(
              valueListenable: _activeTabIndexNotifier,
              builder: (context, activeTabIndex, child) {
                pmLog('🧩 Building BottomNavigationBar');

                return BottomNavigationBar(
                  iconSize: 25.0,
                  items: <BottomNavigationBarItem>[
                    _bottomNavBarItems[0],
                    _bottomNavBarItems[1],
                    BottomNavigationBarItem(
                      icon: Icon(
                        Ionicons.add_circle,
                        size: 30.0,
                        color: context.colors.brandColor,
                      ),
                      label: "",
                    ),
                    _bottomNavBarItems[2],
                    _bottomNavBarItems[3],
                  ],
                  currentIndex: activeTabIndex,
                  selectedFontSize: 12,
                  unselectedFontSize: 12,
                  onTap: (int index) {
                    _handleNavItemTap(index, activeTabIndex);
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _handleNavItemTap(int index, int currentlyActiveTabIndex) async {
    FocusScope.of(context).unfocus();

    if (index == currentlyActiveTabIndex) {
      if (index == 0 && _homeScreenRefreshNotifier.value != null) {
        _homeScreenRefreshNotifier.value!();
      } else if (index == 1 && _searchScreenRefreshNotifier.value != null) {
        _searchScreenRefreshNotifier.value!();
      } else if (index == 3 &&
          _featuredPhotosScreenRefreshNotifier.value != null) {
        _featuredPhotosScreenRefreshNotifier.value!();
      }

      return;
    }

    if (index == 2 || index == 4) {
      if (index == 2) {
        _showUploadForm();
        return;
      } else if (index == 4) {
        _gotoNotificationsScreen(LocalUserService.userId ?? 0);
      }

      return;
    }

    _activeTabIndexNotifier.value = index;
  }

  void _gotoNotificationsScreen(int profileId) {
    NotificationUtil().markAsRead(ref);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NotificationsScreen(profileId: profileId),
      ),
    );
  }

  void _showUploadForm() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder: (BuildContext context) {
        return UploadPhotoModal();
      },
    );
  }

  Widget _buildCustomScreen() {
    return const SizedBox.shrink();
  }
}
