// following_photos_tab_content.dart
// This screen displays photo feed

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/utils/log_util.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_slider.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/home/<USER>/empty_following_notice.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/following_photos_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class FollowingPhotosTabContent extends ConsumerStatefulWidget {
  const FollowingPhotosTabContent({
    super.key,
    required this.refreshNotifier,
    this.onPhotoTwoFingersOn,
    this.onPhotoTwoFingersOff,
  });

  final ValueNotifier<VoidCallback?> refreshNotifier;
  final VoidCallback? onPhotoTwoFingersOn;
  final VoidCallback? onPhotoTwoFingersOff;

  @override
  FollowingPhotosTabContentState createState() =>
      FollowingPhotosTabContentState();
}

class FollowingPhotosTabContentState
    extends ConsumerState<FollowingPhotosTabContent>
    with AutomaticKeepAliveClientMixin<FollowingPhotosTabContent> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  final PhotoListService _photoListService = PhotoListService();
  final ArtistListService _artistListService = ArtistListService();

  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.itemsPerPage;
  int _loadMoreLastId = 0;
  int _currentPageNumber = 0;
  bool _loadMoreEndReached = false;

  final _isFetchingNotifier = ValueNotifier(false);
  final ValueNotifier<List<ArtistData>> _recentlyUploadedArtistsNotifier =
      ValueNotifier([]);
  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  final List<int> _currentlyViewedPhotoIds = [];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    widget.refreshNotifier.value = _scrollToTop;

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    _scrollController.dispose();
    _isFetchingNotifier.dispose();
    _blockScrollNotifier.dispose();

    super.dispose();
  }

  double _getLoadMoreTriggerPoint(double maxExtent) {
    // Calculate how much of the content should remain when triggering load more
    double remainingContent = maxExtent * LoadMoreConfig.tresholdPercentage;

    // Apply min/max constraints
    remainingContent = remainingContent.clamp(
      LoadMoreConfig.minTreshold,
      LoadMoreConfig.maxTreshold,
    );

    // Return the scroll position that should trigger load more
    return maxExtent - remainingContent;
  }

  bool _canLoadMore() {
    return !_isFetchingNotifier.value && !_loadMoreEndReached;
  }

  void _onScroll() {
    // Safe some resource by early checking.
    if (!_canLoadMore()) return;

    double triggerPoint = _getLoadMoreTriggerPoint(
      _scrollController.position.maxScrollExtent,
    );

    // Handle load more when scrolling reaches the trigger point
    if (_scrollController.position.pixels >= triggerPoint) {
      if (_canLoadMore()) _triggerLoadMore();
    }
  }

  void _triggerLoadMore() async {
    _isFetchingNotifier.value = true;
    await _loadMore();
    _isFetchingNotifier.value = false;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    pmLog('🖥️ Building FollowingPhotosTabContent');

    final bool behaveNewlyOpened = ref.watch(
      appStateProvider.select((data) => data.behaveNewlyOpened),
    );

    if (behaveNewlyOpened) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToTop();

        if (mounted) {
          // Reset the behaveNewlyOpened flag after handling it.
          ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
        }
      });
    }

    return Container(
      color: context.colors.scaffoldColor,
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        color: context.colors.brandColor,
        elevation: 0.0,
        onRefresh: _handleRefresh,
        child: ValueListenableBuilder(
          valueListenable: _isFetchingNotifier,
          builder: (context, isFetching, child) {
            final int totalFollowing = ref.watch(
              profileProvider.select((value) => value.totalFollowing),
            );

            final List<PhotoData> photoList = ref.watch(
              followingPhotosProvider,
            );

            if (totalFollowing < 1 ||
                (photoList.isEmpty && _loadMoreEndReached)) {
              return Center(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: const EmptyFollowingNotice(),
                ),
              );
            }

            return ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return ListView.builder(
                  controller: _scrollController,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  cacheExtent: getVerticalScrollCacheExtent(context),
                  itemCount:
                      photoList.length +
                      (isFetching && !_loadMoreEndReached ? 1 : 0),
                  itemBuilder: (BuildContext context, int index) {
                    if (index == photoList.length &&
                        isFetching &&
                        !_loadMoreEndReached) {
                      return Container(
                        margin: EdgeInsets.only(
                          top: photoList.isEmpty ? 0 : 16.0,
                        ),
                        height: 2.7,
                        child: LinearProgressIndicator(
                          color: context.colors.baseColorAlt,
                        ),
                      );
                    }

                    double marginTop = index == 0
                        ? LayoutConfig.contentTopGap
                        : 12.0;

                    return (index == 18
                        ? Column(
                            key: ValueKey(photoList[index]),
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                  top: marginTop,
                                  bottom: 20.0,
                                ),
                                child: ValueListenableBuilder(
                                  valueListenable:
                                      _recentlyUploadedArtistsNotifier,
                                  builder:
                                      (
                                        context,
                                        recentlyUploadedArtists,
                                        child,
                                      ) {
                                        return ArtistListSlider(
                                          artistList: recentlyUploadedArtists,
                                        );
                                      },
                                ),
                              ),
                              PhotoListItem(
                                photo: photoList[index],
                                isOwnProfile:
                                    photoList[index].authorId == _profileId,
                                screenName: 'explore_screen',
                                onTwoFingersOn: () {
                                  _blockScrollNotifier.value = true;
                                  widget.onPhotoTwoFingersOn?.call();
                                },
                                onTwoFingersOff: () {
                                  _blockScrollNotifier.value = false;
                                  widget.onPhotoTwoFingersOff?.call();
                                },
                              ),
                            ],
                          )
                        : PhotoListItem(
                            key: ValueKey(photoList[index]),
                            photo: photoList[index],
                            margin: EdgeInsets.only(top: marginTop),
                            isOwnProfile:
                                photoList[index].authorId == _profileId,
                            screenName: 'explore_screen',
                            onTwoFingersOn: () {
                              _blockScrollNotifier.value = true;
                              widget.onPhotoTwoFingersOn?.call();
                            },
                            onTwoFingersOff: () {
                              _blockScrollNotifier.value = false;
                              widget.onPhotoTwoFingersOff?.call();
                            },
                          ));
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  void _scrollToTop() async {
    scrollListToTop(_scrollController, _refreshIndicatorKey);
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;
    _loadMoreLastId = 0;
    _isFetchingNotifier.value = true;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetchFromFollowedArtists(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
      ),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    final PhotoListResponse photoListResponse = reponses[0];

    _handlePhotoListResponse(photoListResponse, true);
    _isFetchingNotifier.value = false;
  }

  Future<void> _loadMore() async {
    bool shouldUpdateArtistList = false;
    List<dynamic> responses = [];

    late PhotoListResponse photoListResponse;
    late ArtistListResponse artistListResponse;

    final List<int> viewedPhotoIds = _currentPageNumber > 0
        ? _currentlyViewedPhotoIds
        : [];

    if (_currentPageNumber == 1) {
      shouldUpdateArtistList = true;

      responses = await Future.wait([
        _photoListService.fetchFromFollowedArtists(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          viewedPhotoIds: viewedPhotoIds,
        ),
        _artistListService.fetch(limit: 12),
      ]);

      photoListResponse = responses[0];
      artistListResponse = responses[1];
    } else {
      photoListResponse = await _photoListService.fetchFromFollowedArtists(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
        viewedPhotoIds: viewedPhotoIds,
      );
    }

    // Prevent Riverpod error in case user navigates back immediately before async requests completed.
    if (!mounted) return;

    _handlePhotoListResponse(photoListResponse, false);

    if (shouldUpdateArtistList &&
        artistListResponse.success &&
        artistListResponse.data.isNotEmpty) {
      _recentlyUploadedArtistsNotifier.value = artistListResponse.data;
    }
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      _currentlyViewedPhotoIds.clear();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final followingPhotosReactiveService = ref.read(
      followingPhotosReactiveServiceProvider,
    );

    final bool isFirstLoad = _loadMoreLastId == 0 || _loadMoreLastId == -1;

    if (response.data.isEmpty) {
      _loadMoreEndReached = true;
      _currentlyViewedPhotoIds.clear();

      if (isRefresh || isFirstLoad) {
        followingPhotosReactiveService.clear();
      }

      return;
    }

    // Update the list of currently viewed photo ids.
    _currentlyViewedPhotoIds.clear();
    _currentlyViewedPhotoIds.addAll(
      response.data.map((photo) => photo.id).toList(),
    );

    if (response.data.length < _loadMorePerPage) {
      _loadMoreEndReached = true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh || isFirstLoad) {
      _currentPageNumber = 1;
      followingPhotosReactiveService.replaceAll(response.data);
    } else {
      _currentPageNumber++;
      followingPhotosReactiveService.addItems(response.data);
    }
  }
}
