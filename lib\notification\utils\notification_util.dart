import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/notification/dto/notification_data.dart';
import 'package:portraitmode/notification/dto/notification_fetch_result.dart';
import 'package:portraitmode/notification/http_responses/notification_fetch_response.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/notification/services/notification_list_service.dart';
import 'package:portraitmode/notification/services/notification_service.dart';

/// The methods in this class are intended to be called without `await`.
/// So they don't block the code execution.
class NotificationUtil {
  NotificationListService notificationListService = NotificationListService();
  NotificationService notificationService = NotificationService();

  /// Fetch notifications from server.
  ///
  /// Even though this is an async method,
  /// it is intended to be called without "await".
  /// So they don't block the code execution.
  ///
  /// It's fine since notifications fetching is nice to work in the background.
  Future<void> fetch(WidgetRef ref, int artistId) async {
    if (!ref.context.mounted) return;
    NotificationFetchResponse response = await notificationListService.fetch();

    if (!response.success) {
      // pmLog(response.message);
      return;
    }

    if (response.data == null) {
      // pmLog(response.message);
      return;
    }

    NotificationFetchResult result = response.data!;

    // pmLog('Total unread notifications from TabsScreen is: ${result.totalUnread.toString()}');

    if (!ref.context.mounted) return;

    ref.read(notificationProvider.notifier).replace(result);
  }

  /// Mark unread notifications as read.
  ///
  /// Even though this is an async method,
  /// it is intended to be called without "await".
  /// So they don't block the code execution.
  ///
  /// It's fine since marking notifications as read is
  /// nice to work in the background.
  Future<void> markAsRead(WidgetRef ref) async {
    NotificationFetchResult result = ref.read(notificationProvider);

    if (result.notifications.isEmpty) return;
    if (result.totalUnread == 0) return;

    List<int> notifIds = [];

    for (NotificationData notif in result.notifications) {
      if (notif.isRead == 0) {
        notifIds.add(notif.id);
      }
    }

    BaseResponse response = await notificationService.read(notifIds);

    if (!response.success) {
      // pmLog(response.message);
    }

    ref.read(notificationProvider.notifier).updateReadStatuses(notifIds);
    ref.read(notificationProvider.notifier).updateTotalUnread(0);
    ref.read(notificationProvider.notifier).updateTotalRead(result.totalFound);
  }
}
