// id_ids_map_notifier.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';

abstract class IdIdsMapNotifier
    extends AutoDisposeNotifier<Map<int, List<int>>> {
  @override
  Map<int, List<int>> build() => {};

  void _updateState(Map<int, List<int>> newState) {
    state = newState;
  }

  bool hasItem(int id) => state.containsKey(id);

  List<int>? getIds(int id) => state[id];

  List<int> get keys => state.keys.toList();

  List<int> get allIds => state.values.expand((ids) => ids).toList();

  void addItem(int keyId, int id, {bool reorder = false}) {
    final updated = Map<int, List<int>>.from(state);

    if (updated.containsKey(keyId)) {
      if (!updated[keyId]!.contains(id)) {
        updated[keyId] = [...updated[keyId]!, id];
      }
    } else {
      updated[keyId] = [id];
    }

    if (reorder && updated[keyId] != null) {
      updated[keyId]!.sort((a, b) => b.compareTo(a));
    }

    _updateState(updated);
  }

  void addItems(int keyId, List<int> ids, {bool reorder = false}) {
    if (ids.isEmpty) return;
    final updated = Map<int, List<int>>.from(state);

    if (updated.containsKey(keyId)) {
      final existingIds = updated[keyId]!.toSet();
      final newIds = ids.where((id) => !existingIds.contains(id)).toList();

      if (newIds.isNotEmpty) {
        updated[keyId] = [...updated[keyId]!, ...newIds];
      }
    } else {
      updated[keyId] = [...ids];
    }

    if (reorder && updated[keyId] != null) {
      updated[keyId]!.sort((a, b) => b.compareTo(a));
    }

    _updateState(updated);
  }

  void addItemsMap(Map<int, List<int>> newItems) {
    if (newItems.isEmpty) return;
    final updated = Map<int, List<int>>.from(state);
    for (final entry in newItems.entries) {
      final keyId = entry.key;
      final ids = entry.value;
      if (updated.containsKey(keyId)) {
        final existingIds = updated[keyId]!.toSet();
        final newIds = ids.where((id) => !existingIds.contains(id)).toList();
        if (newIds.isNotEmpty) {
          updated[keyId] = [...updated[keyId]!, ...newIds];
        }
      } else {
        updated[keyId] = [...ids];
      }
    }
    _updateState(updated);
  }

  void updateItems(int keyId, List<int> ids) {
    if (!state.containsKey(keyId)) return;
    final updated = Map<int, List<int>>.from(state);
    updated[keyId] = [...ids];
    _updateState(updated);
  }

  void upsertItems(int keyId, List<int> ids) {
    final updated = Map<int, List<int>>.from(state);
    updated[keyId] = [...ids];
    _updateState(updated);
  }

  void removeItem(int keyId) {
    if (!state.containsKey(keyId)) return;
    final updated = Map<int, List<int>>.from(state);
    updated.remove(keyId);
    _updateState(updated);
  }

  void removeItems(List<int> keyIds) {
    if (keyIds.isEmpty) return;
    final updated = Map<int, List<int>>.from(state);
    for (final keyId in keyIds) {
      updated.remove(keyId);
    }
    _updateState(updated);
  }

  void removeIdFromKey(int keyId, int id) {
    if (!state.containsKey(keyId)) return;
    final updated = Map<int, List<int>>.from(state);
    updated[keyId] = updated[keyId]!
        .where((existingId) => existingId != id)
        .toList();
    if (updated[keyId]!.isEmpty) {
      updated.remove(keyId);
    }
    _updateState(updated);
  }

  void removeIdFromAllKeys(int id) {
    final updated = Map<int, List<int>>.from(state);

    for (final entry in updated.entries) {
      entry.value.remove(id);

      if (entry.value.isEmpty) {
        updated.remove(entry.key);
      }
    }

    _updateState(updated);
  }

  void replaceAll(Map<int, List<int>> newMap) {
    _updateState(newMap);
  }

  void clear() {
    _updateState({});
  }

  // Getter methods for better performance.

  int get length => state.length;

  bool get isEmpty => state.isEmpty;

  bool get isNotEmpty => state.isNotEmpty;

  int getIdsCount(int keyId) => state[keyId]?.length ?? 0;

  int get totalIdsCount => state.values.fold(0, (sum, ids) => sum + ids.length);
}
