// archived_photos_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class ArchivedPhotoIdsNotifier extends IdListNotifier {}

final archivedPhotoIdsProvider =
    NotifierProvider.autoDispose<ArchivedPhotoIdsNotifier, List<int>>(
      ArchivedPhotoIdsNotifier.new,
    );

final archivedPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(archivedPhotoIdsProvider);
  if (ids.isEmpty) return <PhotoData>[];

  return ref.read(photoStoreProvider.notifier).getItems(ids);
});

/// High-level service for managing archived photos reactivity.
///
/// This is the recommended way to manage latest photos.
final class ArchivedPhotosReactiveService {
  const ArchivedPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get local photo list
  List<PhotoData> getAll() {
    return ref.read(archivedPhotosProvider);
  }

  /// Get local id list
  List<int> getAllIds() {
    return ref.read(archivedPhotoIdsProvider);
  }

  /// Add a new photo (adds to both global store and local ids)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to local id list
    ref.read(archivedPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(archivedPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a local photo
  void remove(int photoId) {
    ref.read(archivedPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple local photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from local id list
    ref.read(archivedPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in local id list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all local photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace local id list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(archivedPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  // Clear the local id list
  void clear() {
    ref.read(archivedPhotoIdsProvider.notifier).clear();
  }

  /// Get local id list count
  int get count => ref.read(archivedPhotoIdsProvider.notifier).length;

  /// Check if local id list is empty
  bool get isEmpty => ref.read(archivedPhotoIdsProvider.notifier).isEmpty;

  /// Check if local id list is not empty
  bool get isNotEmpty => ref.read(archivedPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the ArchivedPhotosReactiveService.
final archivedPhotosReactiveServiceProvider =
    Provider.autoDispose<ArchivedPhotosReactiveService>((ref) {
      return ArchivedPhotosReactiveService(ref);
    });
