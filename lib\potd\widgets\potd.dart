import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/misc.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/potd/widgets/potd_item.dart';

class Potd extends StatelessWidget {
  final PhotoData? photo;
  final bool isLoading;

  const Potd({super.key, this.photo, this.isLoading = false});

  @override
  Widget build(BuildContext context) {
    return !isLoading && photo == null
        ? const SizedBox.shrink()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: ScreenStyleConfig.horizontalPadding,
                ),
                child: Text(
                  'Photo of the day',
                  style: TextStyle(
                    fontSize: SliderConfig.sliderTitleFontSize,
                    fontWeight: SliderConfig.sliderTitleFontWeight,
                  ),
                ),
              ),
              const SizedBox(height: 10.0),
              !isLoading
                  ? PotdItem(
                      sizing: ImageSizing.ratioSize,
                      photo: photo!,
                      useBorderRadius: false,
                      screenName: 'potd',
                    )
                  : Container(
                      height: 250.0,
                      color: context.colors.baseColorAlt,
                    ),
            ],
          );
  }
}
