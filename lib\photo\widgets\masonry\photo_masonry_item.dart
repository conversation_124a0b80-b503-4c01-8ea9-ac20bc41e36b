import 'package:flutter/material.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_frame.dart';

class PhotoMasonryItem extends StatelessWidget {
  const PhotoMasonryItem({
    super.key,
    required this.index,
    this.containerWidth,
    required this.photo,
    this.isOwnProfile = false,
    required this.screenName,
    this.isSearchScreen = false,
    this.onPhotoTap,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
  });

  final int index;
  final double? containerWidth;
  final PhotoData photo;
  final bool isOwnProfile;
  final String screenName;
  final bool isSearchScreen;
  final VoidCallback? onPhotoTap;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PhotoFrame(
          isRounded: true,
          photo: photo,
          isOwnPhoto: isOwnProfile,
          screenName: screenName,
          isSmall: true,
          showMetadata: false,
          zoomable: false,
          onTwoFingersOn: onTwoFingersOn,
          onTwoFingersOff: onTwoFingersOff,
          onTap: onPhotoTap,
        ),
      ],
    );
  }
}
