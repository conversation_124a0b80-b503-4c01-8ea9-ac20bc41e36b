import 'package:flutter/widgets.dart';
import 'package:portraitmode/app/config/colors.dart';

class ParagraphText extends StatelessWidget {
  final String text;
  final BuildContext context;
  final bool isCentered;

  const ParagraphText({
    super.key,
    required this.text,
    required this.context,
    this.isCentered = false,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      textAlign: isCentered ? TextAlign.center : TextAlign.start,
      style: TextStyle(
        fontSize: 14.0,
        height: 1.4,
        fontWeight: FontWeight.w500,
        color: context.colors.brandColorAlt,
      ),
    );
  }
}
