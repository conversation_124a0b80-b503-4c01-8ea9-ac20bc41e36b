import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/widgets/bottom_sheets/report_comment_bottom_sheet.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';

class OtherCommentBottomSheet extends ConsumerStatefulWidget {
  final CommentData comment;
  final Function(String)? onCommentReported;

  const OtherCommentBottomSheet({
    super.key,
    required this.comment,
    this.onCommentReported,
  });

  @override
  OtherCommentBottomSheetState createState() => OtherCommentBottomSheetState();
}

class OtherCommentBottomSheetState
    extends ConsumerState<OtherCommentBottomSheet> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> menuItems = [];

    menuItems.addAll([
      ModalListTile(
        title: "Report comment",
        textColor: context.colors.dangerColor,
        iconColor: context.colors.dangerColor,
        iconData: Ionicons.notifications_outline,
        onTap: _handleReportCommentTap,
      ),
    ]);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: BottomSheetConfig.verticalSpace),
      child: SafeArea(
        child: SizedBox(
          height: BottomSheetConfig.menuItemHeight * menuItems.length,
          child: Column(children: menuItems),
        ),
      ),
    );
  }

  void _handleReportCommentTap() {
    Navigator.pop(context);
    _showReportPhotoModal();
  }

  void _showReportPhotoModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder: (BuildContext context) {
        return ReportCommentBottomSheet(
          commentToReport: widget.comment,
          onCommentReported: widget.onCommentReported,
        );
      },
    );
  }
}
