import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_chip.dart';
import 'package:portraitmode/app/widgets/pm_choice_chip.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_search_field.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class CategoriesPicker extends ConsumerStatefulWidget {
  const CategoriesPicker({
    super.key,
    this.selectedCategories = const [],
    required this.onClose,
  });

  final List<CategoryData> selectedCategories;
  final Function(List<CategoryData>) onClose;

  @override
  CategoriesPickerState createState() => CategoriesPickerState();
}

class CategoriesPickerState extends ConsumerState<CategoriesPicker> {
  final _scrollController = ScrollController();
  final _categoryListService = CategoryListService();
  final _searchFieldController = TextEditingController();

  final int maxSelectedCategories = 10;

  final ValueNotifier<bool> _isLoadingNotifier = ValueNotifier(true);

  List<CategoryData> _categories = [];

  late final ValueNotifier<List<CategoryData>> _renderedCategoriesNotifier;
  late final ValueNotifier<List<CategoryData>> _selectedCategoriesNotifier;

  @override
  void initState() {
    _renderedCategoriesNotifier = ValueNotifier(widget.selectedCategories);
    _selectedCategoriesNotifier = ValueNotifier(widget.selectedCategories);

    _handleOnInit();
    super.initState();
  }

  @override
  void dispose() {
    _searchFieldController.dispose();
    _scrollController.dispose();
    _isLoadingNotifier.dispose();

    _renderedCategoriesNotifier.dispose();
    _selectedCategoriesNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(ScreenStyleConfig.horizontalPadding),
          child: SizedBox(
            height: 40.0,
            child: ValueListenableBuilder(
              valueListenable: _selectedCategoriesNotifier,
              builder: (context, selectedCategories, child) {
                return ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.all(6.0),
                    foregroundColor: Colors.white,
                    backgroundColor: context.colors.accentColor,
                    elevation: 0.0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                  onPressed: () {
                    widget.onClose(_selectedCategoriesNotifier.value);
                    Navigator.pop(context, _selectedCategoriesNotifier.value);
                  },
                  child: const Text('Confirm'),
                );
              },
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 768.0),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                PmSliverAppBar(
                  scrollController: _scrollController,
                  titleText: "Select a category",
                  useLogo: false,
                  automaticallyImplyLeading: true,
                  floating: false,
                  pinned: true,
                  actions: const [],
                ),

                SliverPersistentHeader(
                  floating: false,
                  pinned: true,
                  delegate: DelegateCategorySearchField(
                    controller: _searchFieldController,
                    onChanged: _handleSearch,
                  ),
                ),

                SliverToBoxAdapter(
                  child: RefreshIndicator(
                    color: context.colors.brandColor,
                    elevation: 0.0,
                    onRefresh: _handleRefresh,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: ValueListenableBuilder(
                        valueListenable: _isLoadingNotifier,
                        builder: (context, isLoading, child) {
                          return isLoading
                              ? _buildLoadingChips()
                              : _buildChips();
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingChips() {
    const sizes = [60, 80, 90, 110];
    Random random = Random();
    Color loadingColor = context.colors.baseColorAlt;

    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: [
        for (var i = 0; i < 25; i++)
          PmChip(
            label: SizedBox(
              width: sizes[random.nextInt(sizes.length)].toDouble(),
            ),
            backgroundColor: loadingColor,
          ),
      ],
    );
  }

  Widget _buildChips() {
    return ValueListenableBuilder(
      valueListenable: _renderedCategoriesNotifier,
      builder: (context, renderedCategories, child) {
        return ValueListenableBuilder(
          valueListenable: _selectedCategoriesNotifier,
          builder: (context, selectedCategories, child) {
            return Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                for (final category in renderedCategories)
                  PmChoiceChip(
                    label: Text(category.name),
                    selected: selectedCategories.any(
                      (element) => element.id == category.id,
                    ),
                    onSelected: (bool selected) {
                      _handleChipSelected(selected, category);
                    },
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _handleChipSelected(bool selected, CategoryData category) {
    if (selected) {
      if (_renderedCategoriesNotifier.value.length != _categories.length) {
        _searchFieldController.clear();
      }

      if (_selectedCategoriesNotifier.value.length >= maxSelectedCategories) {
        _selectedCategoriesNotifier.value = _selectedCategoriesNotifier.value
            .where((element) => element.id != category.id)
            .toList();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'You can only select up to $maxSelectedCategories categories.',
            ),
            duration: const Duration(seconds: 2),
          ),
        );

        return;
      }
    }

    if (selected) {
      _selectedCategoriesNotifier.value = [
        ..._selectedCategoriesNotifier.value,
        category,
      ];
      _renderedCategoriesNotifier.value = _categories;
    } else {
      _selectedCategoriesNotifier.value = _selectedCategoriesNotifier.value
          .where((element) => element.id != category.id)
          .toList();
    }

    FocusScope.of(context).unfocus();
  }

  void _handleOnInit() async {
    CategoryListResponse response = await _categoryListService.fetch();
    _handleResponse(response);
  }

  void _handleSearch(String value) {
    if (value.isEmpty) {
      _renderedCategoriesNotifier.value = _categories;
      return;
    }

    final List<CategoryData> results = _categories.where((cat) {
      return cat.name.toLowerCase().contains(value.toLowerCase());
    }).toList();

    _renderedCategoriesNotifier.value = results;
  }

  Future<void> _handleRefresh() async {
    _isLoadingNotifier.value = true;

    CategoryListResponse response = await _categoryListService.fetch();

    _handleResponse(response);
  }

  void _handleResponse(CategoryListResponse response) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
        return;
      }
    }

    _categories = response.data;
    _renderedCategoriesNotifier.value = response.data;

    _isLoadingNotifier.value = false;
  }
}
