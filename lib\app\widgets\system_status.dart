import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/latest_issue_modal.dart';
import 'package:portraitmode/system_status/dto/system_status_data.dart';
import 'package:portraitmode/system_status/dto/system_status_history_data.dart';

class SystemStatus extends StatelessWidget {
  final String title;
  final SystemStatusData? data;
  final bool isLoading;

  const SystemStatus({
    super.key,
    this.title = "System status",
    this.data,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    Color statusColor = Colors.transparent;
    Color loadingColor = context.colors.lightColor;
    String statusText = 'Normal';

    if (!isLoading && data != null) {
      statusColor = data!.currentStatus == "normal"
          ? context.colors.successColor
          : data!.currentStatus == "warning"
          ? context.colors.warningColor
          : context.colors.dangerColor;

      statusText = data!.currentStatus;

      if (statusText == 'issue') {
        statusText = 'Issue detected';
      }
    }

    statusText = statusText[0].toUpperCase() + statusText.substring(1);

    return Padding(
      padding: const EdgeInsets.only(left: 13.0, right: 13.0, bottom: 25.0),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: SliderConfig.sliderTitleFontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              if (!isLoading && data != null) {
                if (data!.currentIssue != null) {
                  _showLatestIssue(context, data!.currentIssue!);
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 8.0,
                horizontal: 14.0,
              ),
              // Use statusColor.withOpacity(0.1) for bg, and statusColor for the text and border color.
              decoration: BoxDecoration(
                color: isLoading
                    ? loadingColor
                    : statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(5.0),
                border: Border.all(
                  color: isLoading ? loadingColor : statusColor,
                  width: 1.0,
                ),
              ),
              child: Text(
                !isLoading && data != null ? statusText : "Normal",
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: statusColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLatestIssue(BuildContext context, SystemStatusHistoryData issue) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder: (BuildContext context) {
        return LatestIssueModal(issue: issue);
      },
    );
  }
}
