import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class PmChoiceChip extends StatelessWidget {
  final Widget label;
  final bool selected;
  final bool showCheckmark;
  final Function(bool)? onSelected;

  const PmChoiceChip({
    super.key,
    required this.label,
    this.selected = false,
    this.showCheckmark = false,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return ChoiceChip(
      label: label,
      labelStyle: TextStyle(
        color: selected
            ? context.colors.accentColor
            : context.colors.brandColor,
      ),
      elevation: 0.0,
      backgroundColor: context.colors.baseColorAlt,
      selectedColor: context.isDarkMode
          ? AppColorsCache.dark().baseColorAlt.withAlpha(70)
          : AppColorsCache.light().baseColor,
      showCheckmark: showCheckmark,
      selected: selected,
      onSelected: onSelected,
      // These properties are to override Material 3 styling.
      side: BorderSide.none,
      shape: StadiumBorder(),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
}
