import 'package:flutter/widgets.dart';

enum FollowActionType { follow, unfollow }

extension AssignmentActionEnumExtension on FollowActionType {
  String get text {
    switch (this) {
      case FollowActionType.follow:
        return 'follow';
      case FollowActionType.unfollow:
        return 'unfollow';
    }
  }
}

enum BlockActionType { block, unblock }

extension BlockActionEnumExtension on BlockActionType {
  String get text {
    switch (this) {
      case BlockActionType.block:
        return 'block';
      case BlockActionType.unblock:
        return 'unblock';
    }
  }
}

// `AlbumLoadingNotification` is used to indicate whether
// a loading indicator should be shown, rather than triggering
// an actual "load more" operation.

@immutable
class AlbumLoadingNotification extends Notification {
  final bool value;

  const AlbumLoadingNotification(this.value);
}
