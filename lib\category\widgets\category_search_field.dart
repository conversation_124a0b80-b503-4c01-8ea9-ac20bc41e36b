import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

class DelegateCategorySearchField extends SliverPersistentHeaderDelegate {
  DelegateCategorySearchField({
    required this.controller,
    required this.onChanged,
  });

  final TextEditingController controller;
  final void Function(String)? onChanged;

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: ScreenStyleConfig.horizontalPadding,
        vertical: ScreenStyleConfig.verticalPadding,
      ),
      child: TextFormField(
        controller: controller,
        onChanged: (value) {
          if (onChanged != null) {
            onChanged!(value);
          }
        },
        autofocus: true,
        decoration: InputDecoration(
          hintText: "Search...",
          contentPadding: EdgeInsets.symmetric(vertical: 0.0, horizontal: 15),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(width: 1, color: context.colors.baseColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(width: 1, color: context.colors.greyColor),
          ),
        ),
      ),
    );
  }

  @override
  double get maxExtent => 60.0;

  @override
  double get minExtent => 60.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}
