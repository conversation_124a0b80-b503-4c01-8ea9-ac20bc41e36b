import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/widgets/pm_chip.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_detail_screen_masonry_mode.dart';

class PhotoCategories extends StatefulWidget {
  final List<int> categoryIds;
  final EdgeInsetsGeometry? padding;
  final bool hasDivider;
  final double contentToDividerGap;

  const PhotoCategories({
    super.key,
    required this.categoryIds,
    this.padding,
    this.hasDivider = true,
    this.contentToDividerGap = 8.0,
  });

  @override
  PhotoCategoriesState createState() => PhotoCategoriesState();
}

class PhotoCategoriesState extends State<PhotoCategories> {
  final CategoryListService _categoryListService = CategoryListService();
  late Future<CategoryListResponse> _categoryListResponse;

  @override
  void initState() {
    super.initState();

    _categoryListResponse = _categoryListService.fetchSpecific(
      categoryIds: widget.categoryIds,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CategoryListResponse>(
      future: _categoryListResponse,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (!snapshot.hasError && snapshot.hasData) {
            CategoryListResponse response = snapshot.data;

            if (response.success) {
              return _buildCategoryList(
                isLoading: false,
                categoryList: response.data,
              );
            }
          }
        }

        return _buildCategoryList(isLoading: true, categoryList: []);
      },
    );
  }

  Widget _buildCategoryList({
    bool isLoading = true,
    List<CategoryData> categoryList = const [],
  }) {
    return Padding(
      padding: widget.padding != null
          ? widget.padding!
          : const EdgeInsets.all(0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text("Categories: ", style: TextStyle(fontSize: 15.0)),
          const SizedBox(height: 5.0),
          Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: [
              if (isLoading)
                for (int i = 0; i < widget.categoryIds.length; i++)
                  _buildLoadingChip(),
              if (!isLoading)
                for (CategoryData category in categoryList)
                  _buildCategoryChip(context, category),
            ],
          ),
          if (widget.hasDivider) SizedBox(height: widget.contentToDividerGap),
          if (widget.hasDivider)
            Divider(height: 1.0, color: context.colors.borderColor),
        ],
      ),
    );
  }

  Widget _buildLoadingChip() {
    return PmChip(
      label: const SizedBox(width: 60.0),
      backgroundColor: context.colors.scaffoldColor,
    );
  }

  ActionChip _buildCategoryChip(BuildContext context, CategoryData category) {
    return ActionChip(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                CategoryDetailScreenMasonryMode(category: category),
          ),
        );
      },
      label: Text(category.name, style: const TextStyle(fontSize: 12)),
      backgroundColor: context.colors.scaffoldColor,
      visualDensity: VisualDensity.compact,
      shape: RoundedRectangleBorder(
        side: BorderSide(
          color: context.isDarkMode
              ? AppColorsCache.dark().baseColorAlt
              : AppColorsCache.light().baseColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(30.0),
      ),
    );
  }
}
