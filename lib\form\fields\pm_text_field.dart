// pm_text_field.dart

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class PmTextField extends StatelessWidget {
  const PmTextField({
    super.key,
    this.controller,
    this.initialValue,
    this.textCapitalization = TextCapitalization.none,
    this.labelText = '',
    this.hintText = '',
    this.readOnly = false,
    this.keyboardType,
    this.obscureText = false,
    this.autofocus = false,
    this.maxLines = 1,
    this.minLines,
    this.validator,
    this.onChanged,
    this.onTap,
  });

  final TextEditingController? controller;
  final String? initialValue;
  final TextCapitalization textCapitalization;
  final String labelText;
  final String? hintText;
  final bool readOnly;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool autofocus;
  final int? maxLines;
  final int? minLines;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      initialValue: initialValue,
      textCapitalization: textCapitalization,
      readOnly: readOnly,
      keyboardType: keyboardType,
      obscureText: obscureText,
      autofocus: autofocus,
      maxLines: maxLines,
      minLines: minLines,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: validator,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText ?? labelText,
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            width: 1,
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(width: 1, color: context.colors.greyColor),
        ),
      ),
      onEditingComplete: () async {
        FocusScope.of(context).nextFocus();
      },
      onChanged: onChanged,
      onTap: onTap,
    );
  }
}
